package cn.bctools.screen.service.impl;

import cn.bctools.screen.entity.ChartTemplate;
import cn.bctools.screen.mapper.ChartTemplateMapper;
import cn.bctools.screen.service.ChartTemplateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 模板
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class ChartTemplateServiceImpl extends ServiceImpl<ChartTemplateMapper, ChartTemplate> implements ChartTemplateService {


}
