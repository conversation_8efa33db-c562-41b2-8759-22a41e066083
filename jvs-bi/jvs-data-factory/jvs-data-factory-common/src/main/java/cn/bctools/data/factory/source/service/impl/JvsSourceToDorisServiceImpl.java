package cn.bctools.data.factory.source.service.impl;

import cn.bctools.data.factory.source.entity.JvsSourceToDoris;
import cn.bctools.data.factory.source.mapper.JvsSourceToDorisMapper;
import cn.bctools.data.factory.source.service.JvsSourceToDorisService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description doris字段映射
 */
@Service
@Slf4j
public class JvsSourceToDorisServiceImpl extends ServiceImpl<JvsSourceToDorisMapper, JvsSourceToDoris> implements JvsSourceToDorisService {

}
