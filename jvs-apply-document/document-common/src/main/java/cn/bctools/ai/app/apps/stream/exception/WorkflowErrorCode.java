package cn.bctools.ai.app.apps.stream.exception;

import lombok.Getter;

/**
 * 工作流错误代码枚举
 *
 * <AUTHOR>
 */
@Getter
public enum WorkflowErrorCode {

    /**
     * 业务错误
     */
    BUSINESS_ERROR("WORKFLOW_BUSINESS_ERROR", "业务逻辑错误"),

    /**
     * 系统错误
     */
    SYSTEM_ERROR("WORKFLOW_SYSTEM_ERROR", "系统内部错误"),

    /**
     * 验证错误
     */
    VALIDATION_ERROR("WORKFLOW_VALIDATION_ERROR", "参数验证错误"),

    /**
     * 资源不足错误
     */
    RESOURCE_ERROR("WORKFLOW_RESOURCE_ERROR", "系统资源不足"),

    /**
     * 超时错误
     */
    TIMEOUT_ERROR("WORKFLOW_TIMEOUT_ERROR", "执行超时"),

    /**
     * 应用类型错误
     */
    INVALID_APP_TYPE("WORKFLOW_INVALID_APP_TYPE", "无效的应用类型"),

    /**
     * 处理器不存在
     */
    PROCESSOR_NOT_FOUND("WORKFLOW_PROCESSOR_NOT_FOUND", "未找到合适的处理器"),

    /**
     * 用户未认证
     */
    USER_NOT_AUTHENTICATED("WORKFLOW_USER_NOT_AUTHENTICATED", "用户未认证"),

    /**
     * 应用不存在
     */
    APP_NOT_FOUND("WORKFLOW_APP_NOT_FOUND", "应用不存在"),

    /**
     * 工作流未发布
     */
    WORKFLOW_NOT_PUBLISHED("WORKFLOW_NOT_PUBLISHED", "工作流未发布"),

    /**
     * 并发限制
     */
    CONCURRENT_LIMIT_EXCEEDED("WORKFLOW_CONCURRENT_LIMIT_EXCEEDED", "并发执行数量超限");

    /**
     * 错误代码
     */
    private final String code;

    /**
     * 错误描述
     */
    private final String description;

    WorkflowErrorCode(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
