package cn.bctools.ai.app.apps.stream.context;

import cn.bctools.design.rule.utils.RuleSystemThreadLocal;
import cn.bctools.common.utils.SystemThreadLocal;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * ThreadLocal管理工具类
 * 防止内存泄漏的ThreadLocal管理
 *
 * <AUTHOR>
 */
@Slf4j
public class ThreadLocalManager {

    /**
     * 备份当前ThreadLocal状态
     */
    public static ThreadLocalBackup backup() {
        try {
            Map<String, Object> systemThreadLocal = new HashMap<>(SystemThreadLocal.get());
            Object ruleThreadLocal = RuleSystemThreadLocal.getRule();
            return new ThreadLocalBackup(systemThreadLocal, ruleThreadLocal);
        } catch (Exception e) {
            log.error("❌ 备份ThreadLocal失败", e);
            return new ThreadLocalBackup(new HashMap<>(), null);
        }
    }

    /**
     * 恢复ThreadLocal状态
     */
    public static void restore(ThreadLocalBackup backup) {
        try {
            if (backup != null) {
                // 清理当前状态
                clear();
                
                // 恢复状态
                if (backup.getSystemThreadLocal() != null) {
                    SystemThreadLocal.setAll(backup.getSystemThreadLocal());
                }
                if (backup.getRuleThreadLocal() != null) {
                    RuleSystemThreadLocal.set(backup.getRuleThreadLocal());
                }
            }
        } catch (Exception e) {
            log.error("❌ 恢复ThreadLocal失败", e);
        }
    }

    /**
     * 清理ThreadLocal
     */
    public static void clear() {
        try {
            // 清理规则相关
            RuleSystemThreadLocal.clear();
            
            // 清理以"rule"开头的系统ThreadLocal
            Map<String, Object> current = SystemThreadLocal.get();
            if (current != null) {
                current.entrySet().removeIf(entry -> 
                    entry.getKey().toLowerCase().startsWith("rule"));
            }
        } catch (Exception e) {
            log.error("❌ 清理ThreadLocal失败", e);
        }
    }

    /**
     * 完全清理所有ThreadLocal
     */
    public static void clearAll() {
        try {
            SystemThreadLocal.clear();
            RuleSystemThreadLocal.clear();
        } catch (Exception e) {
            log.error("❌ 完全清理ThreadLocal失败", e);
        }
    }

    /**
     * 执行带ThreadLocal管理的操作
     */
    public static void executeWithBackup(Runnable runnable) {
        ThreadLocalBackup backup = backup();
        try {
            runnable.run();
        } finally {
            restore(backup);
        }
    }

    /**
     * ThreadLocal备份类
     */
    public static class ThreadLocalBackup {
        private final Map<String, Object> systemThreadLocal;
        private final Object ruleThreadLocal;

        public ThreadLocalBackup(Map<String, Object> systemThreadLocal, Object ruleThreadLocal) {
            this.systemThreadLocal = systemThreadLocal;
            this.ruleThreadLocal = ruleThreadLocal;
        }

        public Map<String, Object> getSystemThreadLocal() {
            return systemThreadLocal;
        }

        public Object getRuleThreadLocal() {
            return ruleThreadLocal;
        }
    }
}
