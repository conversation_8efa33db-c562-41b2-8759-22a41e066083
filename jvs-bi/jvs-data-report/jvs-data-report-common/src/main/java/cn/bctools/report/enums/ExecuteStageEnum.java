package cn.bctools.report.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExecuteStageEnum {
    pending("pending","等待执行"),
    executing("executing","正在执行"),
    error("error","执行异常"),
    success("success","执行成功"),
    ;

    @EnumValue
    private final String code;
    private final String desc;
}
