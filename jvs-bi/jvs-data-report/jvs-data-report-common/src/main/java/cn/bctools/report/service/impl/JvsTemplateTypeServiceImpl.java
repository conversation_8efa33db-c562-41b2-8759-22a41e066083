package cn.bctools.report.service.impl;

import cn.bctools.report.entity.JvsTemplateType;
import cn.bctools.report.mapper.JvsTemplateTypeMapper;
import cn.bctools.report.service.JvsTemplateTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 模板分类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class JvsTemplateTypeServiceImpl extends ServiceImpl<JvsTemplateTypeMapper, JvsTemplateType> implements JvsTemplateTypeService {


}
