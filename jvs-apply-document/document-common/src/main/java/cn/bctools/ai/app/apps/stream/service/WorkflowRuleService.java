package cn.bctools.ai.app.apps.stream.service;

import cn.bctools.common.utils.*;
import cn.bctools.design.rule.RuleStartUtils;
import cn.bctools.design.rule.entity.*;
import cn.bctools.design.rule.service.RunLogService;
import cn.bctools.redis.utils.RedisUtils;
import cn.bctools.rule.entity.enums.RunType;
import cn.bctools.rule.error.MessageTipsDto;
import cn.bctools.rule.utils.RuleSystemThreadLocal;
import cn.bctools.rule.utils.UrlUtils;
import cn.bctools.rule.utils.dto.RuleExecDto;
import cn.bctools.rule.utils.html.RuleExecuteDto;
import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.dto.StreamExecutionContext;
import cn.bctools.stream.manager.StreamExecutionManager;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.ai.app.entity.AppDetail;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * 工作流执行逻辑服务类
 * 专门用于处理工作流的逻辑引擎调用和流式返回
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class WorkflowRuleService {

    /**
     * 规则启动工具类，用于启动逻辑引擎
     */
    private final RuleStartUtils ruleStartUtils;
    /**
     * 运行日志服务类，用于处理运行日志相关业务
     */
    private final RunLogService runLogService;
    private final RedisUtils redisUtils;
    /**
     * 流式执行管理器
     */
    private final StreamExecutionManager streamExecutionManager;

    /**
     * 规则运行缓存 key 格式
     */
    private final static String RULE_KEY_FORMAT = "edf-ai:rule:run:key:%s";
    /**
     * HTTP 协议前缀
     */
    private static final String http = "http";

    /**
     * 执行工作流（新的流式执行入口）
     *
     * @param context      流式执行上下文
     * @param appDetail    应用详情
     * @param userDto      用户信息
     * @param ruleDesignPo 规则设计对象
     * @return 执行结果
     */
    public Object executeWorkflow(StreamExecutionContext context, AppDetail appDetail, UserDto userDto, RuleDesignPo ruleDesignPo) {
        log.info("🚀 开始执行工作流 - 应用ID: {}, 执行ID: {}, 流式类型: {}",
                appDetail.getId(), context.getExecutionId(), context.getOutputType());

        try {
            // 使用流式执行逻辑
            return executeWorkflowWithStreaming(context, appDetail, ruleDesignPo, userDto);

        } catch (Exception e) {
            log.error("❌ 工作流执行失败 - 应用ID: {}, 执行ID: {}, 错误: {}",
                    appDetail.getId(), context.getExecutionId(), e.getMessage(), e);

            // 通知流式管理器执行失败
            streamExecutionManager.onWorkflowFailed(context.getExecutionId(), e.getMessage());
            throw e;
        }
    }

    /**
     * 使用流式处理执行工作流（改进版本 - 解决ThreadLocal内存泄漏）
     */
    private Object executeWorkflowWithStreaming(StreamExecutionContext context, AppDetail appDetail,
                                                RuleDesignPo ruleDesignPo, UserDto userDto) {
        // 备份当前ThreadLocal状态
        Map<String, Object> originalThreadLocal = new HashMap<>(SystemThreadLocal.get());
        Object originalRuleThreadLocal = RuleSystemThreadLocal.getRule();

        try {
            // 设置租户上下文
            TenantContextHolder.setTenantId(ruleDesignPo.getTenantId());

            // 设置流式执行上下文
            SystemThreadLocal.set(StreamConstants.AttributeNames.CONTEXT, context);

            // 设置应用ID
            SystemThreadLocal.set("jvsAppId", appDetail.getId());

            // 设置执行ID
            SystemThreadLocal.set("executionId", context.getExecutionId());

            //根据参数获取的值
            RuleSystemThreadLocal.setParameterSelectedOption(context.getInputParams());

        // 准备执行参数
        Map<String, Object> ruleVariable = new HashMap<>(16);

        //获取请求体传参
        if (ObjectNull.isNotNull(context.getInputParams())) {
            ruleVariable.putAll(context.getInputParams());
        }

        // 获取请求路径上的参数(优先级最高)
        Map<String, Object> urlParams = UrlUtils.getUrlParams();
        if (ObjectUtils.isNotEmpty(urlParams)) {
            ruleVariable.putAll(urlParams);
        }

        // 创建运行日志
        RunLogPo logPo = runLogService.create(appDetail.getId(), ruleDesignPo.getSecret(),
                RunType.API, ruleVariable, ruleDesignPo.getReqType(), ruleDesignPo.getReqType(), false);
        logPo.setTenantId(ruleDesignPo.getTenantId());

        // 准备执行数据
        ruleVariable.put("ruleKey", ruleDesignPo.getSecret());
        RuleExecuteDto data = new RuleExecuteDto()
                .setReqVariableMap(context.getInputParams())
                .setVariableMap(ruleVariable);

        // 准备逻辑执行DTO
        RuleExecDto ruleExecDto = new RuleExecDto()
                .setExecuteDto(data)
                .setType(RunType.API)
                .setSecret(ruleDesignPo.getSecret())
                .setGraph(JSONObject.parseObject(ruleDesignPo.getDesignDrawingJson(), cn.bctools.rule.utils.html.HtmlGraph.class));

            // 根据流式输出类型处理
            return handleStreamingExecution(context, ruleDesignPo, logPo, ruleExecDto);

        } catch (Exception e) {
            log.error("❌ 工作流流式执行失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), e.getMessage(), e);
            throw e;
        } finally {
            // 恢复原始ThreadLocal状态，防止内存泄漏
            try {
                // 清理当前设置的ThreadLocal
                SystemThreadLocal.remove(StreamConstants.AttributeNames.CONTEXT);
                SystemThreadLocal.remove("executionId");
                RuleSystemThreadLocal.clear();

                // 清理以"rule"开头的ThreadLocal变量
                Map<String, Object> currentThreadLocal = SystemThreadLocal.get();
                if (currentThreadLocal != null) {
                    currentThreadLocal.entrySet().removeIf(entry ->
                        entry.getKey().toLowerCase().startsWith("rule"));
                }

                // 恢复原始状态
                SystemThreadLocal.setAll(originalThreadLocal);
                if (originalRuleThreadLocal != null) {
                    RuleSystemThreadLocal.set(originalRuleThreadLocal);
                }

                log.debug("🧹 ThreadLocal状态已恢复 - 执行ID: {}", context.getExecutionId());

            } catch (Exception cleanupException) {
                log.error("❌ ThreadLocal清理失败 - 执行ID: {}, 错误: {}",
                         context.getExecutionId(), cleanupException.getMessage());
            }
        }
    }

    /**
     * 处理流式执行
     */
    private Object handleStreamingExecution(StreamExecutionContext context, RuleDesignPo ruleDesignPo,
                                            RunLogPo logPo, RuleExecDto ruleExecDto) {
        try {
            streamExecutionManager.startExecution(context);

            SystemThreadLocal.set(StreamConstants.AttributeNames.CONTEXT, context);
            // 根据输出类型返回相应的响应
            return switch (context.getOutputType()) {
                case FLUX -> handleFluxExecution(context, ruleDesignPo, logPo, ruleExecDto);
                case SSE -> handleSseExecution(context, ruleDesignPo, logPo, ruleExecDto);
                case WEBSOCKET -> handleWebSocketExecution(context, ruleDesignPo, logPo, ruleExecDto);
                // 默认同步执行
                default -> executeSynchronously(ruleDesignPo, logPo, ruleExecDto);
            };

        } catch (Exception e) {
            log.error("❌ 流式执行处理失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), e.getMessage(), e);
            streamExecutionManager.onWorkflowFailed(context.getExecutionId(), e.getMessage());
            throw new RuntimeException("流式执行失败", e);
        }
    }

    /**
     * 处理Flux流式执行
     */
    private Object handleFluxExecution(StreamExecutionContext context, RuleDesignPo ruleDesignPo,
                                       RunLogPo logPo, RuleExecDto ruleExecDto) {
        log.info("🌊 开始Flux流式执行 - 执行ID: {}", context.getExecutionId());

        // 异步执行规则
        try {

            // 执行逻辑引擎
            ruleStartUtils.start(ruleDesignPo, logPo, ruleExecDto);

            // 获取执行结果
            Object result = getExecutionResult(ruleExecDto);

            // 通知工作流完成
            streamExecutionManager.onWorkflowCompleted(context.getExecutionId(), result);

            log.info("✅ Flux流式执行完成 - 执行ID: {}", context.getExecutionId());

        } catch (Exception e) {
            log.error("❌ Flux流式执行失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), e.getMessage(), e);
            streamExecutionManager.onWorkflowFailed(context.getExecutionId(), e.getMessage());
        }

        // 返回Flux流
        return context.getFluxSink();
    }

    /**
     * 处理SSE流式执行
     */
    private Object handleSseExecution(StreamExecutionContext context, RuleDesignPo ruleDesignPo,
                                      RunLogPo logPo, RuleExecDto ruleExecDto) {
        log.info("📡 开始SSE流式执行 - 执行ID: {}", context.getExecutionId());

        try {
            // 执行逻辑引擎
            ruleStartUtils.start(ruleDesignPo, logPo, ruleExecDto);

            // 获取执行结果
            Object result = getExecutionResult(ruleExecDto);

            // 通知工作流完成
            streamExecutionManager.onWorkflowCompleted(context.getExecutionId(), result);

            log.info("✅ SSE流式执行完成 - 执行ID: {}", context.getExecutionId());

        } catch (Exception e) {
            log.error("❌ SSE流式执行失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), e.getMessage(), e);
            streamExecutionManager.onWorkflowFailed(context.getExecutionId(), e.getMessage());
        }

        // 返回SSE发射器
        return context.getSseEmitter();
    }

    /**
     * 处理WebSocket流式执行
     */
    private Object handleWebSocketExecution(StreamExecutionContext context, RuleDesignPo ruleDesignPo,
                                            RunLogPo logPo, RuleExecDto ruleExecDto) {
        log.info("🔌 开始WebSocket流式执行 - 执行ID: {}", context.getExecutionId());

        try {

            // 执行逻辑引擎
            ruleStartUtils.start(ruleDesignPo, logPo, ruleExecDto);

            // 获取执行结果
            Object result = getExecutionResult(ruleExecDto);

            // 通知工作流完成
            streamExecutionManager.onWorkflowCompleted(context.getExecutionId(), result);

            log.info("✅ WebSocket流式执行完成 - 执行ID: {}", context.getExecutionId());

        } catch (Exception e) {
            log.error("❌ WebSocket流式执行失败 - 执行ID: {}, 错误: {}", context.getExecutionId(), e.getMessage(), e);
            streamExecutionManager.onWorkflowFailed(context.getExecutionId(), e.getMessage());
        }

        return context.getWebSocketSession();
    }

    /**
     * 同步执行规则
     */
    private Object executeSynchronously(RuleDesignPo ruleDesignPo, RunLogPo logPo, RuleExecDto ruleExecDto) {
        log.info("🔄 开始同步执行 - 规则: {}", ruleDesignPo.getSecret());

        try {
            // 执行逻辑引擎
            ruleStartUtils.start(ruleDesignPo, logPo, ruleExecDto);

            // 处理执行结果
            return processExecutionResult(ruleExecDto);

        } catch (Exception e) {
            log.error("❌ 同步执行失败 - 规则: {}, 错误: {}", ruleDesignPo.getSecret(), e.getMessage(), e);
            throw new RuntimeException("同步执行失败", e);
        }
    }

    /**
     * 获取执行结果
     */
    private Object getExecutionResult(RuleExecDto ruleExecDto) {
        if (ObjectNull.isNull(ruleExecDto.getExecuteDto().getEndResult())) {
            return null;
        }

        Object value = ruleExecDto.getExecuteDto().getEndResult().getValue();
        if (value instanceof MessageTipsDto) {
            return ((MessageTipsDto) value).getData();
        }

        return value;
    }

    /**
     * 处理执行结果
     */
    private R processExecutionResult(RuleExecDto ruleExecDto) {
        // 检查是否有异常
        if (ObjectNull.isNotNull(ruleExecDto.getExecuteDto().getException()) &&
                ruleExecDto.getExecuteDto().getException().getCode() == -1007) {
            Object rtnData = null;
            Object msgValue = ruleExecDto.getExecuteDto().getEndResult().getValue();
            if (msgValue instanceof MessageTipsDto) {
                rtnData = Optional.of((MessageTipsDto) msgValue).map(MessageTipsDto::getData).orElse(null);
            }
            return R.failed(rtnData, ruleExecDto.getExecuteDto().getException().getCode(),
                    ruleExecDto.getExecuteDto().getException().getMessage());
        }

        // 检查是否有结果
        if (ObjectNull.isNull(ruleExecDto.getExecuteDto().getEndResult())) {
            return R.ok();
        }

        // 返回结果数据
        Object value = ruleExecDto.getExecuteDto().getEndResult().getValue();
        if (value instanceof MessageTipsDto) {
            MessageTipsDto messageDto = (MessageTipsDto) value;
            return R.ok(messageDto.getData(), messageDto.getMessage());
        }

        return R.ok(value);
    }
}