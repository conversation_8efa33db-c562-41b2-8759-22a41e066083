package cn.bctools.report.service;

import cn.bctools.report.entity.JvsReportTaskLog;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 报表定时任务执行日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
public interface JvsReportTaskLogService extends IService<JvsReportTaskLog> {

    /**
     * 成功
     * @param logId
     */
    void success(String logId);

    /**
     * 异常
     * @param logId
     * @param error
     */
    void failed(String logId,String error);
}
