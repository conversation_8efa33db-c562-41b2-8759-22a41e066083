package cn.bctools.report.data;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.data.factory.api.DataFactoryApi;
import cn.bctools.data.factory.entity.ConsanguinityAnalyse;
import cn.bctools.data.factory.enums.ConsanguinityViewTypeEnum;
import cn.bctools.data.factory.enums.DataFieldTypeClassifyEnum;
import cn.bctools.report.cache.ReportBorderInfoCache;
import cn.bctools.report.cache.ReportExecuteCache;
import cn.bctools.report.cache.ReportMergeCache;
import cn.bctools.report.data.constant.Constant;
import cn.bctools.report.data.mapper.DorisMapper;
import cn.bctools.report.data.po.FieldData;
import cn.bctools.report.data.search.FPage;
import cn.bctools.report.data.search.OrderDto;
import cn.bctools.report.data.search.SqlQueryDto;
import cn.bctools.report.data.util.FillCellDataUtils;
import cn.bctools.report.dto.ReportDataSourceApiDto;
import cn.bctools.report.dto.SubtotalConfigurationDto;
import cn.bctools.report.entity.JvsReport;
import cn.bctools.report.enums.*;
import cn.bctools.report.po.*;
import cn.bctools.report.utils.SortUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.SystemClock;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 数据替换工厂类
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class DataParserFactory {
    private final List<DataParser> dataParsers;
    private final DataFilterFactory dataFilterFactory;
    private final DorisMapper dorisMapper;
    private final RabbitTemplate rabbitTemplate;

    public static final ThreadLocal<Map<String,List<Map<String,Object>>>> TABLE_DATA_CACHE = new ThreadLocal<>();

    public static final ThreadPoolTaskExecutor THREAD_POOL_EXECUTOR;
    static {
        int coreNum = Runtime.getRuntime().availableProcessors();
        int maxCoreNum = coreNum*2;
        log.info("初始化变量执行线程池,核心线程数：{},最大线程数：{}",coreNum,maxCoreNum);
        THREAD_POOL_EXECUTOR = new ThreadPoolTaskExecutor();
        //核心线程数
        THREAD_POOL_EXECUTOR.setCorePoolSize(maxCoreNum);
        //最大线程数
        THREAD_POOL_EXECUTOR.setMaxPoolSize(maxCoreNum);
        //队列大小
        THREAD_POOL_EXECUTOR.setQueueCapacity(1<<10);
        //线程最大空闲时间
        THREAD_POOL_EXECUTOR.setKeepAliveSeconds(30);
        //指定用于新创建的线程名称的前缀。
        THREAD_POOL_EXECUTOR.setThreadNamePrefix("report-handle-");
        // 拒绝策略（一共四种，此处省略）
        THREAD_POOL_EXECUTOR.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 这一步千万不能忘了，否则报错： java.lang.IllegalStateException: ThreadPoolTaskExecutor not initialized
        THREAD_POOL_EXECUTOR.initialize();
    }

    @Value("${report.read.timeout:5}")
    private Long readTimeOut;
    /**
     * 是否异步执行
     * @param jvsReport
     * @param isTask 是否未定时任务执行
     * @return
     */
    public JvsReport exec(JvsReport jvsReport,boolean isTask) {
        String viewJson = jvsReport.getViewJson();
        if (StrUtil.isBlank(viewJson)) {
            return jvsReport;
        }
        ReportDataPo reportDataPo = JSONObject.parseObject(viewJson, ReportDataPo.class);
        //重构数据源结构
        List<ReportDataSourceApiDto> dataSources = jvsReport.getDataSource()
                .stream()
                .map(e -> e.toJavaObject(ReportDataSourceApiDto.class))
                .collect(Collectors.toList());
        if(dataSources.parallelStream().noneMatch(ReportDataSourceApiDto::check)){
            return jvsReport;
        }
        //如果是静态报表 则不限制数量
        //查询数据
        Map<String, List<Map<String, Object>>> data = this.getData(dataSources ,reportDataPo,jvsReport.getReportType());
        //血缘关系
        send(dataSources,jvsReport);
        if (data.isEmpty()) {
            log.info("数据源数据【{}】为空,返回设计数据",dataSources);
            return jvsReport;
        }

        long now = SystemClock.now();
        try {
            JSONObject jsonObject;
            //定时任务执行没有超时时间
            if(isTask){
                jsonObject = THREAD_POOL_EXECUTOR.submit(() -> createReport(reportDataPo, viewJson,data)).get();
            }else{
                Future<JSONObject> submit = THREAD_POOL_EXECUTOR.submit(() -> createReport(reportDataPo, viewJson,data));
                jsonObject = submit.get(readTimeOut, TimeUnit.SECONDS);
            }
            if(jsonObject==null){
                return jvsReport;
            }
            jvsReport.setViewJson(JSONUtil.toJsonStr(jsonObject));
            return jvsReport;
        }catch (TimeoutException timeE){
            timeE.printStackTrace();
            throw new BusinessException("数据处理时间过长,请开启分页或修改为定时报表");
        }catch (Exception e){
            e.printStackTrace();
            throw new BusinessException(e.getMessage());
        }finally {
            log.info("数据处理消耗时间：{}",SystemClock.now()-now);
        }

    }

    @SneakyThrows
    public JSONObject createReport(ReportDataPo reportDataPo,String viewJson, Map<String, List<Map<String, Object>>> data){
        if(StrUtil.isBlank(viewJson)){
            return null;
        }
        TABLE_DATA_CACHE.set(data);
        JSONObject jsonObject = JSONObject.parseObject(viewJson);;
        try {
            //过滤无效的cellData 单元格设置
            List<CellData> collect = reportDataPo.getCelldata().stream().filter(CellData::check).collect(Collectors.toList());
            reportDataPo.setCelldata(collect);
            //填充规则 从上到下从左到右 动态属性组合例外
            List<CellData> cellSettings = sort(reportDataPo.getCelldata());
            //设置cellData属性
            setOtherAttr(cellSettings);
            //初始化数据
            reportDataPo.setNewCellData(new ArrayList<>())
                    .setCalcChain(new ArrayList<>())
                    .setBorderInfo(new ArrayList<>())
                    .setNewConditionFormat(new ArrayList<>());

            //填充数据
            for (DataParser dataParser : dataParsers) {
                cellSettings.stream().filter(dataParser::check).forEach(v -> dataParser.generatedData(v, reportDataPo));
            }

            List<CellData> newData = cellSettings.stream().filter(CellData::getNeedExecute).map(CellData::getExecList).filter(CollectionUtil::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());

            List<CellShowData> showDataList = BeanCopyUtil.copys(newData, CellShowData.class);

            //去除选区
            jsonObject.remove("luckysheet_selection_range");
            jsonObject.remove("data");
            jsonObject.put("celldata", showDataList);
            jsonObject.put("calcChain", reportDataPo.getCalcChain());
            jsonObject.getJSONObject("config").put("borderInfo", ReportBorderInfoCache.get());
            jsonObject.getJSONObject("config").put("merge", ReportMergeCache.get());
            jsonObject.getJSONObject("config").put("columnlen", reportDataPo.getConfig().getColumnlen());
            jsonObject.getJSONObject("config").put("customWidth", reportDataPo.getConfig().getCustomWidth());
            jsonObject.put("customData", reportDataPo.getCustomData());
            jsonObject.put(Constant.CONDITION_FORMAT_KEY,reportDataPo.getNewConditionFormat());
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            TABLE_DATA_CACHE.remove();
            //清除线程中的缓存
            ReportExecuteCache.remove();
            //清除缓存
            ReportMergeCache.remove();
            ReportBorderInfoCache.remove();
        }
        return jsonObject;
    }

    /**
     * 排序
     *
     * @param cellData 整个设计数据
     */
    private List<CellData> sort(List<CellData> cellData) {
        return cellData.stream().sorted(Comparator.comparing(CellData::getR).thenComparing(CellData::getC)).collect(Collectors.toList());
    }

    /**
     * 设置其他属性
     *
     * @param cellSettings 单元格设计
     */
    private List<CellData> setOtherAttr(List<CellData> cellSettings) {
        //设置 单元格类型
        cellSettings.stream()
                .peek(FillCellDataUtils::setSubtotalConfiguration)
                .forEach(e -> e
                        //设置单元格类型
                        .setCellNodeType(getNodeType(e))
                        //设置聚合方式
                        .setAggregate(getAggregate(e))
                        //设置是否为函数
                        .setIsFunction(isFunction(e))
                        //设置数据拓展方向
                        .setExpandDirection(getExpandDirection(e))
                );

        //识别动态组合
        //1.过滤出所有的动态属性 动态属性为纵向 不分组
        List<CellData> dynamicCellAll = cellSettings.stream()
                .filter(e -> CellNodeType.dynamic.equals(e.getCellNodeType()))
                .filter(e -> CellDataAggregateEnum.list.equals(e.getAggregate()))
                .collect(Collectors.toList());
        //判断是否需要执行
        List<CellData> noExecute = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(dynamicCellAll)){
            //2.向上查找分组条件 和标题
            dynamicCellAll.forEach(e -> {
                CellData.DynamicGroup dynamicGroup = new CellData.DynamicGroup();
                List<CellData> top = this.searchUp(e, cellSettings);
                List<CellData> left = this.searchLeft(e, cellSettings);
                List<CellData> dynamic = this.searchDynamic(e, cellSettings);
                List<CellData> title = this.searchTitle(e,dynamic, cellSettings);
                //查找固定标题
                List<CellData> finalCells = new ArrayList<>();
                Optional<CellData> topScope = top.parallelStream().min(Comparator.comparing(CellData::getR));
                Optional<CellData> lefScope = left.parallelStream().min(Comparator.comparing(CellData::getC));
                if (topScope.isPresent() && lefScope.isPresent()) {
                    CellData cellData = topScope.get();
                    CellData lefMin = lefScope.get();
                    Integer minR = cellData.getR();
                    Integer maxC = cellData.getC();
                    Integer maxR = lefMin.getR();
                    Integer minC = lefMin.getC();
                    finalCells = cellSettings.parallelStream()
                            .filter(v ->CellNodeType.staticState.equals(v.getCellNodeType()))
                            .filter(v -> minR <= v.getR() && v.getR() < maxR && minC <= v.getC() && v.getC() < maxC)
                            .map(v -> v.setJoinMerge(false))
                            .collect(Collectors.toList());
                }
                dynamicGroup
                        .setTitle(title)
                        .setTopGroup(top)
                        .setLeftGroup(left)
                        .setDynamicData(dynamic)
                        .setFinalCells(finalCells);
                e.setDynamicGroup(dynamicGroup);
            });

            //判断是否需要执行
            List<CellData> collect = dynamicCellAll.stream().map(CellData::getDynamicGroup)
                    .map(e -> Stream.of(e.getDynamicData(), e.getLeftGroup(), e.getTitle(), e.getTopGroup(),e.getFinalCells()).collect(Collectors.toList()))
                    .flatMap(Collection::stream).flatMap(Collection::stream).collect(Collectors.toList());
            noExecute.addAll(collect);
        }

        //识别分组小计
        List<CellData> subtotalList = cellSettings.stream()
                .filter(e -> CellDataAggregateEnum.list.equals(e.getAggregate()))
                .filter(e -> !CellNodeType.dynamic.equals(e.getCellNodeType()))
                .filter(e -> Optional.of(e).map(CellData::getSubtotalConfiguration)
                        .map(SubtotalConfigurationDto::getStatisticalMethodEnum)
                        .filter(v -> !StatisticalMethodEnum.无.equals(v)).isPresent())
                .collect(Collectors.toList());

        if(CollectionUtil.isNotEmpty(subtotalList)){
            List<CellData> condition = cellSettings.stream()
                    .filter(e -> Optional.of(e).map(CellData::getSubtotalConfiguration)
                            .map(SubtotalConfigurationDto::getStatisticalMethodEnum)
                            .filter(StatisticalMethodEnum.无::equals).isPresent()).collect(Collectors.toList());

            subtotalList.forEach(e -> {
                List<CellData> subtotalCondition = findSubtotalCondition(e, condition);
                List<CellData> subtotalNeighboring = findSubtotalNeighboring(e, cellSettings);
                if(CollectionUtil.isNotEmpty(subtotalCondition) && subtotalCondition.stream().anyMatch(v -> v.getSubtotalConfiguration().getBasis())){
                    e.setCellNodeType(CellNodeType.subtotal);
                    CellData.GroupingSubtotals groupingSubtotals = new CellData.GroupingSubtotals()
                            .setCondition(subtotalCondition)
                            .setStatistics(subtotalNeighboring);
                    e.setGroupingSubtotals(groupingSubtotals);

                    List<CellData> collect = Stream.of(subtotalCondition, subtotalNeighboring).flatMap(Collection::stream).collect(Collectors.toList());
                    noExecute.addAll(collect);
                }
            });

        }
        if(CollectionUtil.isNotEmpty(noExecute)){
            cellSettings.forEach( e -> {
                if(noExecute.stream().anyMatch(v -> v.getR().equals(e.getR()) && v.getC().equals(e.getC()))){
                    e.setNeedExecute(Boolean.FALSE);
                }
            });
        }
        return cellSettings;
    }

    /**
     * 查找分组小计条件
     * 横向拓展 向上查找 列相同
     * 纵向拓展 向左查找 行相同
     * @param setting 分组依据
     * @param cellSettings 单元格设置list
     * @return
     */
    private List<CellData> findSubtotalCondition(CellData setting,List<CellData> cellSettings){
        Integer r = setting.getR();
        Integer c = setting.getC();
        AtomicInteger pedometer = new AtomicInteger(1);
        ExpandDirectionEnum expandDirection = setting.getExpandDirection();
        switch (expandDirection){
            case 横向:
                return cellSettings.stream()
                        .filter(e -> expandDirection.equals(e.getExpandDirection()))
                        .filter(e -> c.equals(e.getC()) && e.getR() < r)
                        .sorted(Comparator.comparing(CellData::getR).reversed())
                        .filter(e -> {
                            Integer interval = r - e.getR();
                            if (interval.equals(pedometer.get())) {
                                pedometer.getAndIncrement();
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());
            case 纵向:
                return cellSettings.stream()
                        .filter(e -> expandDirection.equals(getExpandDirection(e)))
                        .filter(e -> r.equals(e.getR()) && e.getC() < c)
                        .sorted(Comparator.comparing(CellData::getC).reversed())
                        .filter(e -> {
                            Integer interval = c - e.getC();
                            if (interval.equals(pedometer.get())) {
                                pedometer.getAndIncrement();
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());
            default:
                return new ArrayList<>();
        }
    }

    /**
     * 查找相邻的单元格设置
     * 横向拓展 向下查找 列相同
     * 纵向拓展 向右查找 行相同
     * @param setting
     * @param subtotalList
     * @return
     */
    private List<CellData> findSubtotalNeighboring(CellData setting,List<CellData> subtotalList){
        Integer r = setting.getR();
        Integer c = setting.getC();
        AtomicInteger pedometer = new AtomicInteger(1);
        ExpandDirectionEnum expandDirection = setting.getExpandDirection();
        switch (expandDirection){
            case 横向:
                return subtotalList.stream()
                        .filter(e -> c.equals(e.getC()) && e.getR() > r)
                        .filter(e -> expandDirection.equals(e.getExpandDirection()))
                        .sorted(Comparator.comparing(CellData::getR))
                        .filter(e -> {
                            Integer interval = e.getR() - r;
                            if (interval.equals(pedometer.get())) {
                                pedometer.getAndIncrement();
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());
            case 纵向:
                return subtotalList.stream()
                        .filter(e -> r.equals(e.getR()) && e.getC() > c)
                        .filter(e -> expandDirection.equals(getExpandDirection(e)))
                        .sorted(Comparator.comparing(CellData::getC))
                        .filter(e -> {
                            Integer interval = e.getC() - c;
                            if (interval.equals(pedometer.get())) {
                                pedometer.getAndIncrement();
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());
            default:
                return new ArrayList<>();
        }
    }


    /**
     * 获取节点类型
     * @param cellSetting
     * @return
     */
    private CellNodeType getNodeType(CellData cellSetting){
        if(isCrosswise(cellSetting)){
            return CellNodeType.cross;
        }
        if(isDynamic(cellSetting)){
            return CellNodeType.dynamic;
        }
        if(FillCellDataUtils.isStatic(cellSetting)){
            return CellNodeType.staticState;
        }
        return CellNodeType.length;
    }

    /**
     * 获取聚合方式
     * @param cellSetting
     * @return
     */
    private CellDataAggregateEnum getAggregate(CellData cellSetting){
        try {
            Map<String, Object> v = cellSetting.getV();
            if(v.containsKey("aggregate")){
                return CellDataAggregateEnum.valueOf(StrUtil.toString(v.get("aggregate")));
            }
        } catch (Exception e) {
            log.info("单元格聚合方式与系统设计不符,请检查聚合方式标识");
        }
        return CellDataAggregateEnum.list;
    }

    /**
     * 向上查找静态标题
     * @param dynamicList
     * @param all
     * @return
     */
    private List<CellData> searchTitle(CellData current,List<CellData> dynamicList,List<CellData> all){
        List<CellData> cellData = new ArrayList<>(dynamicList);
        cellData.add(current);
        Integer minC = cellData.stream().map(CellData::getC).min(Integer::compare).get();
        Integer maxC = cellData.stream().map(CellData::getC).max(Integer::compare).get();
        return all.stream().filter(e -> CellNodeType.staticState.equals(e.getCellNodeType())).filter(e -> e.getC()>=minC && e.getC()<=maxC).filter(e -> cellData.stream().anyMatch(v -> NumberUtil.sub(v.getR(), e.getR()).intValue() == 1)).collect(Collectors.toList());
    }

    /**
     * 向上查找分组条件或静态标题
     * @param dynamic
     * @param all
     */
    private List<CellData> searchUp(CellData dynamic,List<CellData> all){
        //1.得到动态属性所在列
        Integer dc = dynamic.getC();
        Integer dr = dynamic.getR();

        //2.得到当前列所有的单元格设置 去除纵向拓展的单元格设置
        List<CellData> collect = all.stream()
                .filter(e -> dc.equals(e.getC()))
                .filter(e -> e.getR()<dr)
                .collect(Collectors.toList());

        //3.得分组条件
        Map<Integer, CellData> rowGroup = collect.stream().collect(Collectors.toMap(CellData::getR, Function.identity()));
        List<CellData> conditions = new ArrayList<>();
        //递归查找 遇到空行直接结束
        recursionSearch(dr-1,rowGroup,conditions,CellNodeType.cross);

        return conditions.stream().filter(e -> !CellNodeType.staticState.equals(e.getCellNodeType()) && CellNodeType.cross.equals(e.getCellNodeType())).collect(Collectors.toList());
    }

    /**
     * 向左查找分组条件或静态标题
     * @param dynamic
     * @param all
     */
    private List<CellData> searchLeft(CellData dynamic,List<CellData> all){
        //1.得到动态属性所在列
        Integer dc = dynamic.getC();
        Integer dr = dynamic.getR();

        //2.得到当前行所有的单元格设置 去除纵向拓展的单元格设置
        List<CellData> collect = all.stream()
                .filter(e -> dr.equals(e.getR()))
                .filter(e -> e.getC()<dc)
                .filter(e -> CellNodeType.length.equals(e.getCellNodeType()))
                .collect(Collectors.toList());

        //3.得到静态或分组条件
        Map<Integer, CellData> columnGroup = collect.stream().collect(Collectors.toMap(CellData::getC, Function.identity()));
        List<CellData> conditions = new ArrayList<>();
        //递归查找 遇到空行直接结束
        recursionSearch(dc-1,columnGroup,conditions,CellNodeType.length);

        return conditions;
    }

    /**
     * 向右查找当前的兄弟动态属性
     * @param current
     * @param all
     * @return
     */
    private List<CellData> searchDynamic(CellData current,List<CellData> all){
        Integer dr = current.getR();
        Integer dc = current.getC();

        //删除出所有的动态属性或函数 在动态属性右边的函数也被视为动态属性
        List<CellData> collect = all.stream()
                .filter(e -> dr.equals(e.getR()))
                .filter(e -> e.getC() > dc)
                .filter(e -> CellNodeType.dynamic.equals(e.getCellNodeType()) || e.getIsFunction())
                .collect(Collectors.toList());

        //3.
        Map<Integer, CellData> columnGroup = collect.stream().collect(Collectors.toMap(CellData::getC, Function.identity()));
        List<CellData> dynamicList = new ArrayList<>();
        //递归查找 遇到空行直接结束
        recursionSearch(dc+1,columnGroup,dynamicList,CellNodeType.dynamic);

        dynamicList.stream().filter(CellData::getIsFunction).peek( e ->e.setCellNodeType(CellNodeType.dynamic)).collect(Collectors.toList());
        return dynamicList;
    }

    /**
     * 递归查询复合条件的单元格设置 遇到空直接停止
     * @param index
     * @param group
     * @param collect
     */
    private void recursionSearch(Integer index,Map<Integer, CellData> group,List<CellData> collect,CellNodeType cellNodeType){
        if(group.containsKey(index)){
            CellData cellData = group.get(index);
            collect.add(cellData);
            switch (cellNodeType){
                case cross:
                    index = cellData.getR()-1;
                    break;
                case length:
                    index = cellData.getC()-1;
                    break;
                case dynamic:
                    index = cellData.getC()+1;
                    break;
            }
            recursionSearch(index,group,collect,cellNodeType);
        }
    }

    /**
     * 拓展方向是否为横向 默认是竖向拓展
     * @param cellSetting
     * @return
     */
    private Boolean isCrosswise(CellData cellSetting){
        return Boolean.parseBoolean(Optional.ofNullable(cellSetting.getV().get("crosswiseIs")).map(StrUtil::toString).orElse("false"));
    }

    /**
     * 是否为动态属性
     * @param cellSetting
     * @return
     */
    private Boolean isDynamic(CellData cellSetting){
        return Boolean.parseBoolean(Optional.ofNullable(cellSetting.getV().get("dynamicIs")).map(StrUtil::toString).orElse("false"));
    }

    /**
     * 判断是否为函数
     * @param cellSetting 单元格设置
     * @return
     */
    private Boolean isFunction(CellData cellSetting){
        return ObjectUtil.isNotNull(cellSetting.getV().get(Constant.CELL_FUNCTION_VALUE_KEY));
    }

    /**
     * 获取拓展方向
     * @param setting
     * @return
     */
    private ExpandDirectionEnum getExpandDirection(CellData setting){
        if(isCrosswise(setting)){
            return ExpandDirectionEnum.横向;
        }
        return ExpandDirectionEnum.纵向;
    }

    /**
     * 数据获取
     *
     * @param dataSources 数据源
     */
    private Map<String, List<Map<String, Object>>> getData(List<ReportDataSourceApiDto> dataSources, ReportDataPo reportDataPo,ReportTypeEnum reportType) {
        try {
            Map<String, List<Map<String, Object>>> data = new HashMap<>();
            CustomData customData = reportDataPo.getCustomData();

            List<SearchDataPo> searchList = customData.getSearchDataPoList();
            if(CollectionUtil.isEmpty(searchList)){
                searchList = reportDataPo.getSearchTableData();
            }

            for (ReportDataSourceApiDto dataSource : dataSources) {
                List<Map<String, Object>> tableData = new ArrayList<>();
                //查询当前数据源的排序字段以及类型
                List<OrderDto> order = this.getOrder(reportDataPo,dataSource.getId());
                //查询当前数据源需要查询的字段
                List<FieldData> queryFieldKey = this.getQueryFieldKey(reportDataPo,dataSource.getId());
                //如果查询的字段为空 则直接查询其他数据源
                if(CollectionUtil.isEmpty(queryFieldKey)){
                    data.put(dataSource.getId(),tableData);
                    continue;
                }
                //查询数据 分页或按条件查询所有
                String tableName = dorisMapper.getTableName(dataSource.getExecuteName());
                SqlQueryDto sqlQueryDto = dataFilterFactory.buildSql(searchList,dataSource.getExecuteName());
                //静态报表不限制数据数量
                if(!ReportTypeEnum.statistic.equals(reportType) && customData.getSize()>0 && customData.getCurrent()>0 && customData.getPageIs()){
                    FPage page = dorisMapper.page(customData.getSize(), customData.getCurrent(), tableName,queryFieldKey, sqlQueryDto.getSql(), sqlQueryDto.getArgs(), order);
                    tableData = page.getData();
                    customData.setTotal(page.getTotal());
                }else{
                    tableData = dorisMapper.query(tableName,queryFieldKey, sqlQueryDto.getSql(), sqlQueryDto.getArgs(),order);
                }
                data.put(dataSource.getId(),tableData);
            }
            return data;
        } catch (Exception e) {
            String message = e.getMessage();
            e.printStackTrace();
            if(message.toLowerCase().contains("unknown column")){
                throw new BusinessException("字段不存在");
            }
            if(message.toLowerCase().contains("unknown table")){
                throw new BusinessException("表不存在");
            }
            throw e;
        }
    }

    public void send(List<ReportDataSourceApiDto> dataSources, JvsReport jvsReport) {
        dataSources.forEach(e -> {
            ConsanguinityAnalyse consanguinityAnalyse = new ConsanguinityAnalyse()
                    .setDataFactoryId(e.getId())
                    .setDesignId(jvsReport.getId())
                    .setDesignName(jvsReport.getName())
                    .setTenantId(jvsReport.getTenantId())
                    .setType(3)
                    .setViewType(ConsanguinityViewTypeEnum.report);
            rabbitTemplate.convertAndSend("consanguinity_analyse_task", consanguinityAnalyse);

        });

    }

    private List<OrderDto> getOrder(ReportDataPo reportDataPo,String dataSourceId){
        Stream<CellData> stream = reportDataPo.getCelldata()
                .stream()
                .filter(e -> StrUtil.equals(FillCellDataUtils.getDataSource(e),dataSourceId));
        //判断是否有分组 有分组按分组的排序来
        boolean match = reportDataPo.getCelldata()
                .stream()
                .filter(e -> StrUtil.equals(FillCellDataUtils.getDataSource(e),dataSourceId))
                .anyMatch(e -> CellDataAggregateEnum.groupBy.equals(e.getAggregate()));
        if(match){
            stream = stream.filter(e -> CellDataAggregateEnum.groupBy.equals(e.getAggregate()) && e.getNeedExecute() && !CellNodeType.dynamic.equals(e.getCellNodeType()));
        }
        return stream
                .filter(e -> e.getV().containsKey("sort"))
                .map(e -> {
                    String fieldKey = FillCellDataUtils.getFieldKey(e);
                    SortEnums sortType = SortEnums.asc;
                    if ( !SortUtils.isNoSort(e)) {
                        sortType = SortEnums.valueOf(e.getV().get("sort").toString());
                    }
                    return new OrderDto().setFieldKey(fieldKey).setType(sortType);
                }).collect(Collectors.toList());
    }

    /**
     * 获取当前报表中的字段名称
     * 只查询这些字段
     * @param reportDataPo
     * @return
     */
    private List<FieldData> getQueryFieldKey(ReportDataPo reportDataPo,String dataSourceId){
        return Optional.ofNullable(reportDataPo.getCelldata()).orElse(new ArrayList<>())
                .stream()
                //不为静态变量
                .filter(e -> !FillCellDataUtils.isStatic(e))
                .filter(e -> StrUtil.equals(FillCellDataUtils.getDataSource(e),dataSourceId))
                .map(e -> {
                    String fieldKey = FillCellDataUtils.getFieldKey(e);
                    DataFieldTypeClassifyEnum fieldType = FillCellDataUtils.getFieldType(e);
                    String fieldFormat = FillCellDataUtils.getFieldFormat(e);
                    return new FieldData().setFieldKey(fieldKey).setFieldType(fieldType).setFormat(fieldFormat);
                }).collect(Collectors.toList());
    }
}
