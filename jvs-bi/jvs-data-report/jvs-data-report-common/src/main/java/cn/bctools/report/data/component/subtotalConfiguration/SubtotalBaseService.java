package cn.bctools.report.data.component.subtotalConfiguration;

import cn.bctools.report.data.util.FillCellDataUtils;
import cn.bctools.report.po.CellData;
import cn.hutool.core.util.NumberUtil;

import java.math.BigDecimal;
import java.util.List;

public interface SubtotalBaseService {

    default BigDecimal exec(CellData setting,List<CellData> list){
        BigDecimal[] bigDecimals = list.stream().map(FillCellDataUtils::getCellValue).map(e -> {
            if(NumberUtil.isNumber(e)){
                return new BigDecimal(e);
            }
            return BigDecimal.ZERO;
        }).toArray(BigDecimal[]::new);
        return exec(setting,bigDecimals);
    }

    BigDecimal exec(CellData setting, BigDecimal[] bigDecimals);
}
