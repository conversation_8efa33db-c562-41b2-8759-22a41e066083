//package cn.bctools.report.mongo.service.impl;
//
//import cn.bctools.common.utils.BeanCopyUtil;
//import cn.bctools.report.mongo.entity.MongoCellDoc;
//import cn.bctools.report.mongo.service.StatisticReportService;
//import cn.bctools.report.po.CellData;
//import lombok.AllArgsConstructor;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
//@Service
//@AllArgsConstructor
//public class MongoCellServiceImpl implements StatisticReportService {
//
//    private final MongoTemplate mongoTemplate;
//
//    @Override
//    public void saveBatch(List<CellData> cellDataList, String collectionName) {
//        List<MongoCellDoc> copys = BeanCopyUtil.copys(cellDataList, MongoCellDoc.class);
//        mongoTemplate.insert(copys,collectionName);
//    }
//
//    @Override
//    public List<CellData> findAll(String collectionName) {
//        List<MongoCellDoc> all = mongoTemplate.findAll(MongoCellDoc.class, collectionName);
//        return BeanCopyUtil.copys(all, CellData.class);
//    }
//
//    @Override
//    public void drop(String collectionName) {
//        mongoTemplate.dropCollection(collectionName);
//    }
//
//    @Async
//    @Override
//    public void asyncDrop(List<String> collectionNameList) {
//        for (String collectionName : collectionNameList) {
//            drop(collectionName);
//        }
//    }
//}
