package cn.bctools.report.utils;

import cn.bctools.report.enums.SortEnums;
import cn.bctools.report.po.CellData;
import cn.hutool.core.util.StrUtil;

import java.util.Optional;

public class SortUtils {

    /**
     * 判断是否有排序
     * @param cell 单元格设置
     * @return true 没有排序 false 有排序
     */
    public static Boolean isNoSort(CellData cell){
        String sort = Optional.ofNullable(cell).map(CellData::getV).map(e -> e.get("sort")).map(StrUtil::toString).orElse(null);
        if(StrUtil.isBlank(sort)){
            return true;
        }
        return StrUtil.equals(sort, SortEnums.nosort.name());
    }
}
