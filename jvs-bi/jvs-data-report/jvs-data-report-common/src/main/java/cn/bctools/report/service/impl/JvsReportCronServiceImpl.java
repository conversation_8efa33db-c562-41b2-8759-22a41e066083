package cn.bctools.report.service.impl;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.R;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.report.dto.TaskCronDto;
import cn.bctools.report.dto.XxlJobParameterDto;
import cn.bctools.report.entity.JvsReport;
import cn.bctools.report.enums.CronEnum;
import cn.bctools.report.service.JvsReportCronService;
import cn.bctools.report.service.JvsReportService;
import cn.bctools.xxl.job.api.XxlAdminApi;
import cn.bctools.xxl.job.api.XxlJobInfoDto;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Slf4j
@Service
public class JvsReportCronServiceImpl implements JvsReportCronService {

    public static final String XXL_JOB_HANDLER = "jvs-data-report-job-mgr";

    @Resource
    private JvsReportService jvsReportService;
    @Resource
    XxlAdminApi xxlAdminApi;

    @Value("${xxl.job.accessToken:qNAMzjEUPoqjaOBgaGMUWQUud2GNoqW7}")
    String token = "qNAMzjEUPoqjaOBgaGMUWQUud2GNoqW7";

    @Override
    public void enable(JvsReport jvsReport, TaskCronDto dto) {
        try {
            if(Optional.ofNullable(dto).map(TaskCronDto::getId).isPresent()){
                log.info("已开启定时任务，无法重复开启报表【{}】定时任务",jvsReport.getName());
                return;
            }
            if(!Optional.ofNullable(dto).map(TaskCronDto::getCron).isPresent()){
                String err = StrUtil.format("报表【{}】未设置cron表达式", jvsReport.getName());
                throw new BusinessException(err);
            }
            XxlJobInfoDto xxlJobInfoDto = BeanCopyUtil.copy(dto, XxlJobInfoDto.class);
            XxlJobParameterDto xxlJobParameterDto = new XxlJobParameterDto()
                    .setReportId(jvsReport.getId())
                    .setTenantId(jvsReport.getTenantId())
                    .setCronSetting(dto)
                    .setOperationId(jvsReport.getCreateById())
                    .setOperationName(jvsReport.getCreateBy())
                    ;

            String corn = dto.getCron();
            if(!dto.getIsCustom()){
                Assert.isTrue(StrUtil.isNotBlank(corn),() -> new BusinessException("未选择重复规则"));
                corn = CronEnum.valueOf(corn).getCorn();
            }

            xxlJobInfoDto
                    .setExecutorHandler(XXL_JOB_HANDLER)
                    .setExecutorParam(JSONUtil.toJsonStr(xxlJobParameterDto))
                    .setJobDesc(jvsReport.getDescription())
                    .setScheduleConf(corn);

            R<Integer> r = xxlAdminApi.save(xxlJobInfoDto, token, SpringContextUtil.getApplicationContextName());
            if(r.is()){
                Integer taskId =r.getData();
                dto.setId(taskId);
                //设置定时任务id
                jvsReportService.update(Wrappers.lambdaUpdate(JvsReport.class)
                        .set(JvsReport::getCronSetting,JSONUtil.toJsonStr(dto))
                        .eq(JvsReport::getId,jvsReport.getId()));
                log.info("定时任务启动成功---任务id：{}",taskId);
                return;
            }
            throw new BusinessException(r.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("定时任务启动失败");
        }
    }

    @Override
    public void remove(JvsReport jvsReport) {
        //如果是停止，则需要将定时任务给删除掉
        try {
            TaskCronDto task = jvsReport.getCronSetting();
            if (Optional.ofNullable(task).map(TaskCronDto::getId).isPresent()) {
                Boolean delete = xxlAdminApi.delete(task.getId(), token).getData();
                task.setId(null);
                //去除定时任务id
                jvsReportService.update(Wrappers.lambdaUpdate(JvsReport.class)
                        .set(JvsReport::getCronSetting,JSONUtil.toJsonStr(task))
                        .eq(JvsReport::getId,jvsReport.getId()));
                log.info("删除定时任务,删除状态为:{}", delete);
            }
        } catch (Exception e) {
            log.error("删除定时任务失败", e);
            throw new BusinessException("删除定时任务失败");
        }
    }

    @Override
    public void removeCronById(Long taskCronId) {
        try {
            Boolean delete = xxlAdminApi.delete(taskCronId.intValue() , token).getData();
            log.info("删除定时任务,删除状态为:{}", delete);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("删除定时任务失败", e);
        }
    }


}
