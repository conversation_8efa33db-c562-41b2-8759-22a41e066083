package cn.bctools.report.data.search;

import cn.bctools.data.factory.enums.DataFieldTypeClassifyEnum;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

public interface DataQuery {
    /**
     * 执行 逻辑
     *
     * @param dataFieldTypeEnum 字段类型
     * @param value             对比值
     * @param value1            被对比的值
     */
    boolean exec(DataFieldTypeClassifyEnum dataFieldTypeEnum, String format, Object value, String value1);

    /**
     * 生成sql
     *
     */
    SqlQueryDto generateSql(DataFieldTypeClassifyEnum dataFieldTypeEnum,String key,String value);

    /**
     * 检测数据是否完整
     */
    default boolean check(Object value, String value1) {
        return ObjectUtil.isNotEmpty(value) && StrUtil.isNotEmpty(value1);
    }
}
