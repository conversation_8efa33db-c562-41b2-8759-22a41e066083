package cn.bctools.screen.service.impl;

import cn.bctools.screen.entity.SysMenu;
import cn.bctools.screen.mapper.SysMenuMapper;
import cn.bctools.screen.service.SysMenuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 菜单
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {


}
