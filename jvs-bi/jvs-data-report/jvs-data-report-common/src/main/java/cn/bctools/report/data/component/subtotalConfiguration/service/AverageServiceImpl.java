package cn.bctools.report.data.component.subtotalConfiguration.service;

import cn.bctools.report.data.component.subtotalConfiguration.SubtotalBaseService;
import cn.bctools.report.po.CellData;
import cn.hutool.core.util.NumberUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 平均数
 */
@Service
public class AverageServiceImpl implements SubtotalBaseService {
    @Override
    public BigDecimal exec(CellData setting,BigDecimal[] bigDecimals) {
        BigDecimal sum = NumberUtil.add(bigDecimals);
        return NumberUtil.div(sum,new BigDecimal(bigDecimals.length));
    }
}
