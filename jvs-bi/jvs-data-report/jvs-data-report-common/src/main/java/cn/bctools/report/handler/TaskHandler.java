package cn.bctools.report.handler;

import cn.bctools.report.dto.TaskCronDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;


/**
 * <AUTHOR>
 */
@Slf4j
@MappedTypes({Object.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class TaskHandler extends AbstractJsonTypeHandler<TaskCronDto> {

    public TaskHandler(Class<?> type) {
    }

    @Override
    protected TaskCronDto parse(String json) {
        return JSONObject.parseObject(json, TaskCronDto.class);
    }

    @Override
    protected String toJson(TaskCronDto obj) {
        return JSON.toJSONString(obj, SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullListAsEmpty, SerializerFeature.WriteNullStringAsEmpty);
    }
}