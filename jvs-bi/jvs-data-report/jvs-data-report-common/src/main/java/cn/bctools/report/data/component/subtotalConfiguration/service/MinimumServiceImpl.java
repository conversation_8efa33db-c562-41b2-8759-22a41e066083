package cn.bctools.report.data.component.subtotalConfiguration.service;

import cn.bctools.report.data.component.subtotalConfiguration.SubtotalBaseService;
import cn.bctools.report.po.CellData;
import cn.hutool.core.util.NumberUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 最小值
 */
@Service
public class MinimumServiceImpl implements SubtotalBaseService {
    @Override
    public BigDecimal exec(CellData setting,BigDecimal[] bigDecimals) {
        return NumberUtil.min(bigDecimals);
    }
}
