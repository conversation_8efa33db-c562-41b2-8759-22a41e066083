package cn.bctools.ai.app.apps.stream.metrics;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 工作流执行指标收集器
 * 用于监控和统计工作流执行情况
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Getter
public class WorkflowExecutionMetrics {

    /**
     * 总执行次数
     */
    private final LongAdder totalExecutions = new LongAdder();

    /**
     * 成功执行次数
     */
    private final LongAdder successfulExecutions = new LongAdder();

    /**
     * 失败执行次数
     */
    private final LongAdder failedExecutions = new LongAdder();

    /**
     * 被拒绝的执行次数
     */
    private final LongAdder rejectedExecutions = new LongAdder();

    /**
     * 总执行时间（毫秒）
     */
    private final LongAdder totalExecutionTime = new LongAdder();

    /**
     * 最大执行时间（毫秒）
     */
    private final AtomicLong maxExecutionTime = new AtomicLong(0);

    /**
     * 最小执行时间（毫秒）
     */
    private final AtomicLong minExecutionTime = new AtomicLong(Long.MAX_VALUE);

    /**
     * 最后一次执行时间
     */
    private volatile LocalDateTime lastExecutionTime;

    /**
     * 最后一次成功执行时间
     */
    private volatile LocalDateTime lastSuccessTime;

    /**
     * 最后一次失败执行时间
     */
    private volatile LocalDateTime lastFailureTime;

    /**
     * 增加总执行次数
     */
    public void incrementTotalExecutions() {
        totalExecutions.increment();
        lastExecutionTime = LocalDateTime.now();
        log.debug("📊 总执行次数: {}", totalExecutions.sum());
    }

    /**
     * 记录成功执行
     *
     * @param executionTimeMs 执行时间（毫秒）
     */
    public void recordSuccessfulExecution(long executionTimeMs) {
        successfulExecutions.increment();
        totalExecutionTime.add(executionTimeMs);
        lastSuccessTime = LocalDateTime.now();
        
        // 更新最大执行时间
        maxExecutionTime.updateAndGet(current -> Math.max(current, executionTimeMs));
        
        // 更新最小执行时间
        minExecutionTime.updateAndGet(current -> Math.min(current, executionTimeMs));
        
        log.debug("📊 成功执行 - 次数: {}, 本次耗时: {}ms", successfulExecutions.sum(), executionTimeMs);
    }

    /**
     * 增加失败执行次数
     */
    public void incrementFailedExecutions() {
        failedExecutions.increment();
        lastFailureTime = LocalDateTime.now();
        log.debug("📊 失败执行次数: {}", failedExecutions.sum());
    }

    /**
     * 增加被拒绝的执行次数
     */
    public void incrementRejectedExecutions() {
        rejectedExecutions.increment();
        log.debug("📊 被拒绝执行次数: {}", rejectedExecutions.sum());
    }

    /**
     * 获取成功率
     *
     * @return 成功率（0-100）
     */
    public double getSuccessRate() {
        long total = totalExecutions.sum();
        if (total == 0) {
            return 0.0;
        }
        return (double) successfulExecutions.sum() / total * 100;
    }

    /**
     * 获取失败率
     *
     * @return 失败率（0-100）
     */
    public double getFailureRate() {
        long total = totalExecutions.sum();
        if (total == 0) {
            return 0.0;
        }
        return (double) failedExecutions.sum() / total * 100;
    }

    /**
     * 获取拒绝率
     *
     * @return 拒绝率（0-100）
     */
    public double getRejectionRate() {
        long totalAttempts = totalExecutions.sum() + rejectedExecutions.sum();
        if (totalAttempts == 0) {
            return 0.0;
        }
        return (double) rejectedExecutions.sum() / totalAttempts * 100;
    }

    /**
     * 获取平均执行时间
     *
     * @return 平均执行时间（毫秒）
     */
    public double getAverageExecutionTime() {
        long successful = successfulExecutions.sum();
        if (successful == 0) {
            return 0.0;
        }
        return (double) totalExecutionTime.sum() / successful;
    }

    /**
     * 获取详细统计信息
     *
     * @return 统计信息对象
     */
    public ExecutionStats getDetailedStats() {
        return new ExecutionStats(
                totalExecutions.sum(),
                successfulExecutions.sum(),
                failedExecutions.sum(),
                rejectedExecutions.sum(),
                getSuccessRate(),
                getFailureRate(),
                getRejectionRate(),
                getAverageExecutionTime(),
                maxExecutionTime.get() == 0 ? 0 : maxExecutionTime.get(),
                minExecutionTime.get() == Long.MAX_VALUE ? 0 : minExecutionTime.get(),
                lastExecutionTime,
                lastSuccessTime,
                lastFailureTime
        );
    }

    /**
     * 重置所有指标
     */
    public void reset() {
        totalExecutions.reset();
        successfulExecutions.reset();
        failedExecutions.reset();
        rejectedExecutions.reset();
        totalExecutionTime.reset();
        maxExecutionTime.set(0);
        minExecutionTime.set(Long.MAX_VALUE);
        lastExecutionTime = null;
        lastSuccessTime = null;
        lastFailureTime = null;
        
        log.info("📊 工作流执行指标已重置");
    }

    /**
     * 打印当前统计信息
     */
    public void printStats() {
        ExecutionStats stats = getDetailedStats();
        log.info("📊 工作流执行统计:");
        log.info("   总执行: {}, 成功: {}, 失败: {}, 拒绝: {}", 
                stats.totalExecutions, stats.successfulExecutions, 
                stats.failedExecutions, stats.rejectedExecutions);
        log.info("   成功率: {:.2f}%, 失败率: {:.2f}%, 拒绝率: {:.2f}%", 
                stats.successRate, stats.failureRate, stats.rejectionRate);
        log.info("   平均耗时: {:.2f}ms, 最大耗时: {}ms, 最小耗时: {}ms", 
                stats.averageExecutionTime, stats.maxExecutionTime, stats.minExecutionTime);
    }

    /**
     * 执行统计信息
     */
    public static class ExecutionStats {
        public final long totalExecutions;
        public final long successfulExecutions;
        public final long failedExecutions;
        public final long rejectedExecutions;
        public final double successRate;
        public final double failureRate;
        public final double rejectionRate;
        public final double averageExecutionTime;
        public final long maxExecutionTime;
        public final long minExecutionTime;
        public final LocalDateTime lastExecutionTime;
        public final LocalDateTime lastSuccessTime;
        public final LocalDateTime lastFailureTime;

        public ExecutionStats(long totalExecutions, long successfulExecutions, long failedExecutions,
                             long rejectedExecutions, double successRate, double failureRate,
                             double rejectionRate, double averageExecutionTime, long maxExecutionTime,
                             long minExecutionTime, LocalDateTime lastExecutionTime,
                             LocalDateTime lastSuccessTime, LocalDateTime lastFailureTime) {
            this.totalExecutions = totalExecutions;
            this.successfulExecutions = successfulExecutions;
            this.failedExecutions = failedExecutions;
            this.rejectedExecutions = rejectedExecutions;
            this.successRate = successRate;
            this.failureRate = failureRate;
            this.rejectionRate = rejectionRate;
            this.averageExecutionTime = averageExecutionTime;
            this.maxExecutionTime = maxExecutionTime;
            this.minExecutionTime = minExecutionTime;
            this.lastExecutionTime = lastExecutionTime;
            this.lastSuccessTime = lastSuccessTime;
            this.lastFailureTime = lastFailureTime;
        }
    }
}
