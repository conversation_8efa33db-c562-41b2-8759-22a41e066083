package cn.bctools.report.cache;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;

public class ReportMergeCache {

    private static final ThreadLocal<JSONObject> local = new TransmittableThreadLocal<JSONObject>();

    public static JSONObject get(){
        JSONObject jsonObject = local.get();
        if(ObjectUtil.isNull(jsonObject)){
            return new JSONObject();
        }
        return jsonObject;
    }

    public static void set(JSONObject merge){
        local.set(merge);
    }

    public static void remove(){
        local.remove();
    }
}
