package cn.bctools.report.service.impl;

import cn.bctools.report.entity.JvsTemplateTypeRelation;
import cn.bctools.report.mapper.JvsTemplateTypeRelationMapper;
import cn.bctools.report.service.JvsTemplateTypeRelationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 模板分类与模板数据关系
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class JvsTemplateTypeRelationServiceImpl extends ServiceImpl<JvsTemplateTypeRelationMapper, JvsTemplateTypeRelation> implements JvsTemplateTypeRelationService {


}
