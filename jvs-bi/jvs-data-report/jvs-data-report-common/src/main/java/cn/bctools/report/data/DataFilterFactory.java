package cn.bctools.report.data;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.R;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.data.factory.api.DataFactoryApi;
import cn.bctools.data.factory.api.dto.RowWhereDto;
import cn.bctools.report.data.search.SqlQueryDto;
import cn.bctools.report.po.SearchDataPo;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
@Slf4j
public class DataFilterFactory {

    private final DataFactoryApi dataFactoryApi;

    /**
     * 拼接sql语句
     * @param search 查询条件
     * @param executeName 数据集id
     * @return sql语句
     */
    public SqlQueryDto buildSql(List<SearchDataPo> search,String executeName){
        RowWhereDto authWhere = getAuthWhere(executeName);
        try {
            SqlQueryDto sqlQueryDto = new SqlQueryDto();
            if(StrUtil.isNotBlank(authWhere.getWhereStr())){
                sqlQueryDto.setSql(authWhere.getWhereStr()).setArgs(authWhere.getInParameter());
            }
            if(CollectionUtil.isEmpty(search)){
                return sqlQueryDto;
            }
            List<SearchDataPo> collect = search.stream().filter(e -> Optional.of(e).map(SearchDataPo::getOpenSearch).orElse(false)).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(collect)){
                return sqlQueryDto;
            }
            List<SqlQueryDto> sqlQueryDtos = collect.stream()
                    .filter(e -> StrUtil.isNotBlank(getCompareValue(e)))
                    .map(v -> SpringContextUtil.getBean(v.getSearchType().getBClass()).generateSql(v.getDataFieldTypeClassify(), v.getFieldKey(), getCompareValue(v))).collect(Collectors.toList());
            String searchSql = sqlQueryDtos.stream().map(SqlQueryDto::getSql).collect(Collectors.joining(" AND "));
            searchSql = StrUtil.concat(true, StringPool.LEFT_BRACKET,searchSql,StringPool.RIGHT_BRACKET);
            List<Object> args = sqlQueryDtos.stream().map(SqlQueryDto::getArgs).flatMap(Collection::stream).collect(Collectors.toList());

            String where = "";
            if(StrUtil.isNotBlank(authWhere.getWhereStr())){
                where = StrUtil.concat(true, sqlQueryDto.getSql(),StringPool.SPACE, StringPool.AND,StringPool.SPACE, searchSql);
            }else{
                where = searchSql;
            }

            sqlQueryDto.getArgs().addAll(args);
            return sqlQueryDto.setSql(where);
        } catch (Exception e) {
            e.printStackTrace();
        }
        throw new BusinessException("WHERE语句生成失败");
    }

    private String getCompareValue(SearchDataPo searchDataPo){
        if(Optional.of(searchDataPo).map(SearchDataPo::getSearchValue).isPresent()){
            String s = StrUtil.toString(searchDataPo.getSearchValue());
            if(JSONUtil.isTypeJSON(s)){
                return JSONUtil.toJsonStr(searchDataPo.getSearchValue());
            }
            return s;
        }
        if(Optional.of(searchDataPo).map(SearchDataPo::getSearchDefault).isPresent()){
            String s = StrUtil.toString(searchDataPo.getSearchDefault());
            if(JSONUtil.isTypeJSON(s)){
                return JSONUtil.toJsonStr(searchDataPo.getSearchValue());
            }
            return s;
        }
        return null;
    }

    /**
     * 获取数据权限
     * @param executeName
     * @return
     */
    private RowWhereDto getAuthWhere(String executeName){
        try {
            R<RowWhereDto> authRow = dataFactoryApi.getAuthRow(executeName);
            if(authRow.is()){
                return authRow.getData();
            }
            throw new BusinessException(authRow.getMsg());
        }catch (Exception e){
            log.info("未查询到数据权限语句");
            if(e.getCause()!=null && e.getCause() instanceof BusinessException){
                throw e;
            }
        }
        return new RowWhereDto();
    }

}
