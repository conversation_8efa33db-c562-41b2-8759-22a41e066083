<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.bctools</groupId>
        <artifactId>jvs-data-factory</artifactId>
        <version>2.2</version>
    </parent>

    <groupId>cn.bctools</groupId>
    <artifactId>jvs-data-factory-common</artifactId>

    <dependencies>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-chart-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>data-screen-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-data-report-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-modules-im-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- 用户信息 -->
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-oauth2</artifactId>
        </dependency>


        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-database</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-rabbit</artifactId>
        </dependency>


        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-data-factory-data-dispose</artifactId>
            <version>${project.version}</version>
        </dependency>


        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-design-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>message-push-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
        </dependency>
        <!-- pgsql -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.3.1</version>
        </dependency>
        <!-- mariadb -->
        <dependency>
            <groupId>org.mariadb.jdbc</groupId>
            <artifactId>mariadb-java-client</artifactId>
            <version>3.1.4</version>
        </dependency>
        <!--oracle-->
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>ojdbc6</artifactId>
            <version>11.2.0.3</version>
        </dependency>

        <!--db2-->
        <dependency>
            <groupId>com.ibm.db2.jcc</groupId>
            <artifactId>db2jcc</artifactId>
            <version>db2jcc4</version>
        </dependency>

        <!--sqlServer-->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>9.4.0.jre8</version>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-sync</artifactId>
            <version>4.6.1</version>
        </dependency>
        <!--clickhouse-->
        <dependency>
            <groupId>ru.yandex.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
            <version>0.3.2</version>
        </dependency>
        <!--apache hive-->
        <dependency>
            <groupId>org.apache.hive</groupId>
            <artifactId>hive-jdbc</artifactId>
            <version>4.0.0-alpha-2</version>
        </dependency>
        <!--prestodb presto-->
        <dependency>
            <groupId>com.facebook.presto</groupId>
            <artifactId>presto-jdbc</artifactId>
            <version>0.287</version>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
