package cn.bctools.data.factory.service.impl;

import cn.bctools.data.factory.entity.JvsDataFactoryMenu;
import cn.bctools.data.factory.mapper.JvsDataFactoryMenuMapper;
import cn.bctools.data.factory.service.JvsDataFactoryMenuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 菜单
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class JvsDataFactoryMenuServiceImpl extends ServiceImpl<JvsDataFactoryMenuMapper, JvsDataFactoryMenu> implements JvsDataFactoryMenuService {


}
