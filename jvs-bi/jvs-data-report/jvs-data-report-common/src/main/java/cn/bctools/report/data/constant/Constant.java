package cn.bctools.report.data.constant;

/**
 * <AUTHOR> admin
 */
public interface Constant {
    /**
     * 变量key
     */
    String CELL_DATA_VALUE_KEY = "v";

    String CELL_DATA_RV_KEY = "rv";
    /**
     * 公式key
     */
    String CELL_FUNCTION_VALUE_KEY = "f";
    /**
     * 变量表达式
     */
    String VARIABLE_PATTERN = "\\$\\{(.*?)\\}";
    /**
     * 数字
     */
    String NUMBER_PATTERN = "[0-9]+";
    /**
     * 数字
     */
    String ALPHABET = "[A-Za-z]";

    String CONDITION_FORMAT_KEY = "luckysheet_conditionformat_save";

    String SELECT_SAVE_KEY = "luckysheet_select_save";

    String CELL_RANGE_KEY = "cellrange";

    String CT_KEY = "ct";

    String FCT_KEY = "fct";

    String CT_T_KEY = "t";

    String CT_FA_KEY = "fa";


    /*合并*/
    String MC_KEY = "mc";

    String MC_RS_KEY = "rs";

    String MC_CS_KEY = "cs";
}
