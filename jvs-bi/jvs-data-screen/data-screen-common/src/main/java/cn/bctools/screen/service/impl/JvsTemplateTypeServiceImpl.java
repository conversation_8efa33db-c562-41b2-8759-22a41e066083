package cn.bctools.screen.service.impl;

import cn.bctools.screen.entity.JvsTemplateType;
import cn.bctools.screen.mapper.JvsTemplateTypeMapper;
import cn.bctools.screen.service.JvsTemplateTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 模板分类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class JvsTemplateTypeServiceImpl extends ServiceImpl<JvsTemplateTypeMapper, JvsTemplateType> implements JvsTemplateTypeService {


}
