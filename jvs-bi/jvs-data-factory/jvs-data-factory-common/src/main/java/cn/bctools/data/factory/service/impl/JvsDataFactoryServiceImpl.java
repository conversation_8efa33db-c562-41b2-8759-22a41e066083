package cn.bctools.data.factory.service.impl;

import cn.bctools.auth.api.dto.PersonnelDto;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.IdGenerator;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.common.utils.TenantContextHolder;
import cn.bctools.data.factory.config.DorisJdbcTemplate;
import cn.bctools.data.factory.constant.RedisKey;
import cn.bctools.data.factory.dto.ColumnWhereDto;
import cn.bctools.data.factory.dto.CopyDto;
import cn.bctools.data.factory.dto.DataFactoryTaskMqDto;
import cn.bctools.data.factory.dto.DataSourceField;
import cn.bctools.data.factory.entity.ConsanguinityAnalyse;
import cn.bctools.data.factory.entity.JvsDataAuth;
import cn.bctools.data.factory.entity.JvsDataFactory;
import cn.bctools.data.factory.entity.JvsDataFactoryOut;
import cn.bctools.data.factory.entity.enums.JvsDataAuthTypeEnum;
import cn.bctools.data.factory.entity.enums.OperateMethodEnum;
import cn.bctools.data.factory.entity.enums.QueueTaskTypeEnum;
import cn.bctools.data.factory.enums.ConsanguinityViewTypeEnum;
import cn.bctools.data.factory.enums.OperationEnum;
import cn.bctools.data.factory.mapper.JvsDataFactoryMapper;
import cn.bctools.data.factory.query.QueryExecuteFactory;
import cn.bctools.data.factory.receiver.ConsanguinityAnalyseConsumer;
import cn.bctools.data.factory.receiver.DataFactoryTaskConsumer;
import cn.bctools.data.factory.service.JvsDataAuthService;
import cn.bctools.data.factory.service.JvsDataFactoryOutService;
import cn.bctools.data.factory.service.JvsDataFactoryQueueService;
import cn.bctools.data.factory.service.JvsDataFactoryService;
import cn.bctools.data.factory.source.entity.DataSource;
import cn.bctools.data.factory.source.entity.DataSourceStructure;
import cn.bctools.data.factory.source.enums.DataSourceTypeEnum;
import cn.bctools.data.factory.source.service.DataSourceService;
import cn.bctools.data.factory.source.service.DataSourceStructureService;
import cn.bctools.data.factory.util.AuthUtil;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.redis.utils.RedisUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据etl 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Service
@AllArgsConstructor
@Slf4j
public class JvsDataFactoryServiceImpl extends ServiceImpl<JvsDataFactoryMapper, JvsDataFactory> implements JvsDataFactoryService {

    private final JvsDataFactoryOutService jvsDataFactoryOutService;
    private final RedisUtils redisUtils;
    private final JvsDataAuthService jvsDataAuthService;
    private final DataSourceService dataSourceService;
    private final JvsDataFactoryMapper jvsDataFactoryMapper;
    private final DorisJdbcTemplate dorisJdbcTemplate;
    private final DataSourceStructureService dataSourceStructureService;
    private final AuthUtil<OperationEnum, JvsDataFactory> jvsDataFactoryAuthUtil;
    private final QueryExecuteFactory queryExecuteFactory;


    @Override
    public ColumnWhereDto getRowWhere(String id) {
        ColumnWhereDto columnWhereDto = new ColumnWhereDto().setWhereStr("").setInParameter(new ArrayList<>());
        UserDto user;
        try {
            user = UserCurrentUtils.getCurrentUser();
        } catch (Exception exception) {
            log.info("用户未登录", exception);
            return columnWhereDto;
        }
        JvsDataFactory byId = this.getById(id);
        List<JvsDataAuth> jvsDataAuth = jvsDataAuthService.list(new LambdaQueryWrapper<JvsDataAuth>().eq(JvsDataAuth::getDataFactoryId, id).eq(JvsDataAuth::getAuthType, JvsDataAuthTypeEnum.row));
        if (byId.getCreateById().equals(user.getId()) || jvsDataAuth.isEmpty()) {
            return columnWhereDto;
        }
        List<String> roleList = UserCurrentUtils.init().getRoles();
        //生成sql语句
        StringBuffer whereSql = new StringBuffer();
        List<Object> inParameter = jvsDataAuth.stream().map(e -> e.getAuthData().toJavaObject(JvsDataAuth.ColumnAuthData.class)).flatMap(e -> {
            boolean extracted = extracted(e.getPersonnel(), user, roleList);
            if (extracted) {
                return queryExecuteFactory.execute(e.getQueryDto(), whereSql, Boolean.FALSE).stream();
            }
            return new ArrayList<>().stream();
        }).collect(Collectors.toList());
        if (whereSql.length() == 0) {
            Boolean columnOtherUserVisible = Optional.ofNullable(byId.getRowOtherUserVisible()).orElse(Boolean.FALSE);
            if (columnOtherUserVisible) {
                return columnWhereDto;
            } else {
                throw new BusinessException("您目前没有此数据集的数据权限,请联系此数据集管理人员!");
            }
        }
        columnWhereDto.setWhereStr(whereSql.toString());
        columnWhereDto.setInParameter(inParameter);
        return columnWhereDto;
    }

    /**
     * 验证用户权限是否通过
     *
     * @param personnel 人员信息
     * @param user      当前用户信息
     * @param roleList  当前用户角色信息
     */
    private static boolean extracted(List<PersonnelDto> personnel, UserDto user, List<String> roleList) {
        return personnel.stream()
                .anyMatch(x -> {
                    switch (x.getType()) {
                        case dept:
                            return StrUtil.equals(user.getDeptId(), x.getId());
                        case role:
                            return roleList.contains(x.getId());
                        case user:
                            return x.getId().equals(user.getId());
                        default:
                            return false;
                    }
                });
    }

    @Override
    public List<DataSourceField> getColumn(String id) {
        JvsDataFactory byId = this.getById(id);
        List<JvsDataAuth> jvsDataAuth = jvsDataAuthService.list(new LambdaQueryWrapper<JvsDataAuth>().eq(JvsDataAuth::getDataFactoryId, id).eq(JvsDataAuth::getAuthType, JvsDataAuthTypeEnum.column));
        //这里应该有个排序 然后获取最新的数据
        JvsDataFactoryOut factoryOut = jvsDataFactoryOutService.getOne(new LambdaQueryWrapper<JvsDataFactoryOut>().eq(JvsDataFactoryOut::getDataId, id)
                .orderByDesc(JvsDataFactoryOut::getCreateTime)
                .last("limit 1"));
        if (factoryOut == null) {
            return new ArrayList<>();
        }
        List<DataSourceField> fieldList = factoryOut.getFieldList();
        UserDto user = UserCurrentUtils.getCurrentUser();
        if (byId.getCreateById().equals(user.getId()) || jvsDataAuth.isEmpty()) {
            return fieldList;
        }
        List<String> roleList = UserCurrentUtils.init().getRoles();
        List<String> fieldKeys = jvsDataAuth.stream().map(e -> e.getAuthData().toJavaObject(JvsDataAuth.RowAuthData.class)).flatMap(e -> {
                    boolean anyMatch = extracted(e.getPersonnel(), user, roleList);
                    if (anyMatch) {
                        return e.getQueryDto().stream();
                    }
                    return new ArrayList<DataSourceField>().stream();
                })
                .map(DataSourceField::getFieldKey)
                .distinct().collect(Collectors.toList());
        if (fieldKeys.isEmpty()) {
            Boolean rowOtherUserVisible = Optional.ofNullable(byId.getColumnOtherUserVisible()).orElse(Boolean.FALSE);
            if (rowOtherUserVisible) {
                return fieldList;
            } else {
                List<String> list = jvsDataAuth.stream().map(e -> e.getAuthData().toJavaObject(JvsDataAuth.RowAuthData.class))
                        .flatMap(e -> e.getQueryDto().stream())
                        .map(DataSourceField::getFieldKey).distinct().collect(Collectors.toList());
                //不可见的情况 需要排查不可见的列
                fieldList = fieldList.stream().filter(e -> !list.contains(e.getFieldKey())).collect(Collectors.toList());
                return fieldList;
            }
        }
        return fieldList.stream().filter(e -> fieldKeys.contains(e.getFieldKey())).collect(Collectors.toList());
    }

    @Override
    public JvsDataFactory auth(String id) {
        JvsDataFactory byId = this.getById(id);
        if (byId == null) {
            log.info("为空的数据id为:{}", id);
            JvsDataFactory jvsDataFactory = new JvsDataFactory();
            jvsDataFactory.setOperationList(new ArrayList<>());
            return jvsDataFactory;
        }
        return jvsDataFactoryAuthUtil.auth(byId, null, Arrays.asList(OperationEnum.values()));
    }

    @Override
    public JvsDataFactory delete(String id) {
        JvsDataFactory byId = this.getById(id);
        //判断队列中是否存在此任务 如果存在就无法保存
        JvsDataFactoryQueueService jvsDataFactoryQueueService = SpringContextUtil.getBean(JvsDataFactoryQueueService.class);
        String taskExec = jvsDataFactoryQueueService.isTaskExec(byId);
        if (StrUtil.isNotBlank(taskExec)) {
            throw new BusinessException(taskExec);
        }
        if (byId.getEnable()) {
            throw new BusinessException("启用数据无法删除");
        }
        List<String> tableName = dorisJdbcTemplate.getTableName(id);
        if (!tableName.isEmpty()) {
            //这里应该使用  数据集id 模糊查询表名称
            tableName.forEach(dorisJdbcTemplate::dropForce);
            //同步删除数据源里面的数据集数据
        }
        dataSourceStructureService.remove(new LambdaQueryWrapper<DataSourceStructure>().eq(DataSourceStructure::getExecuteName, id));
        this.removeById(id);
        return byId;
    }

    @Override
    public JvsDataFactory copy(CopyDto copyDto) {
        JvsDataFactory byId = this.getById(copyDto.getId());
        if (ObjectUtil.isNull(byId)) {
            throw new BusinessException("设计不存在");
        }
        List<OperationEnum> check = jvsDataFactoryAuthUtil.check(OperationEnum.编辑, byId.getCreateById(), byId.getRole(), Arrays.asList(OperationEnum.values()), byId.getRoleType());
        if (CollectionUtil.isEmpty(check)) {
            throw new BusinessException("未拥有复制权限");
        }
        String id = IdGenerator.getIdStr();
        byId.setId(id).setType(copyDto.getMenuId()).setName(copyDto.getName());
        byId.setCreateBy(null);
        byId.setCreateTime(null);
        byId.setCreateById(null);
        byId.setUpdateBy(null);
        byId.setEnable(false);
        byId.setTask(null);
        byId.setUpdateTime(null);
        //重新设置每个节点的dataId
        String viewJson = byId.getViewJson();
        if (viewJson != null) {
            JSONObject jsonObject = JSONObject.parseObject(viewJson);
            jsonObject.getList("nodes", JSONObject.class)
                    .stream().peek(e -> e.put("dataId", id))
                    .collect(Collectors.toList());
            viewJson = jsonObject.toString();
        }
        byId.setViewJson(viewJson);
        this.save(byId);
        //添加血缘视图
        ConsanguinityAnalyse consanguinityAnalyse = new ConsanguinityAnalyse()
                .setDataFactoryId(byId.getId())
                .setTenantId(TenantContextHolder.getTenantId())
                .setViewType(ConsanguinityViewTypeEnum.dataFactoryDataSource)
                .setType(2)
                .setDesignId(byId.getId())
                .setDesignName(byId.getName());
        SpringContextUtil.getBean(ConsanguinityAnalyseConsumer.class).send(consanguinityAnalyse);
        return byId;
    }

    @Override
    public void syncTableStructure(JvsDataFactory factory) {
        JvsDataFactoryOut factoryOut = jvsDataFactoryOutService
                .getOne(new LambdaQueryWrapper<JvsDataFactoryOut>()
                        .eq(JvsDataFactoryOut::getDataId, factory.getId())
                        .orderByDesc(JvsDataFactoryOut::getCreateTime).last("limit 1"));
        DataSourceStructure dataSourceStructure = new DataSourceStructure()
                .setName(factory.getName())
                .setExecuteName(factory.getId())
                .setTableNameDesc(factory.getName());
        List<DataSourceStructure.Structure> fields = new ArrayList<>();
        if (ObjectUtil.isNotNull(factoryOut)) {
            List<DataSourceField> objects = JSONArray.parseArray(JSONObject.toJSONString(factoryOut.getFields()), DataSourceField.class);
            fields = objects.stream()
                    .filter(DataSourceField::getIsShow)
                    .map(e -> {
                        DataSourceStructure.Structure structure = new DataSourceStructure.Structure()
                                .setOriginalColumnName(e.getFieldKey())
                                .setDataFieldTypeEnum(e.getFieldType())
                                .setFormat(e.getFormat())
                                .setDataFieldTypeClassify(e.getDataFieldTypeClassify())
                                .setFormatDefault(e.getFormatDefault())
                                .setLength(e.getLength())
                                .setPrecision(e.getPrecision())
                                .setDorisType(e.getDorisType())
                                .setColumnName(e.getFieldKey())
                                .setColumnCount(e.getFieldName());
                        //子表数据
                        if (e.getItems() != null && !e.getItems().isEmpty()) {
                            List<DataSourceStructure.Structure> items = e.getItems().stream().map(v -> new DataSourceStructure.Structure()
                                            .setOriginalColumnName(v.getFieldKey())
                                            .setColumnName(v.getFieldName())
                                            .setDataFieldTypeClassify(v.getDataFieldTypeClassify())
                                            .setDataFieldTypeEnum(v.getFieldType())
                                            .setFormat(v.getFormat())
                                            .setLength(v.getLength())
                                            .setPrecision(v.getPrecision())
                                            .setFormatDefault(v.getFormatDefault())
                                            .setDorisType(v.getDorisType())
                                            .setColumnCount(v.getFieldKey()))
                                    .collect(Collectors.toList());
                            structure.setItems(items);
                        }
                        return structure;
                    })
                    .collect(Collectors.toList());
        }
        dataSourceStructure.setStructure(fields).setFieldCount(fields.size());
        //修改
        //防止没有默认数据
        dataSourceService.saveDataFactory();
        //直接获取数据集
        DataSource one = dataSourceService.getOne(new LambdaQueryWrapper<DataSource>().eq(DataSource::getSourceType, DataSourceTypeEnum.dataFactoryDataSource));
        dataSourceStructure.setDataSourceId(one.getId());
        dataSourceService.updateDataFactoryStructure(dataSourceStructure);
    }

    @Override
    public void sendQueue(JvsDataFactory dataFactory, QueueTaskTypeEnum queueTaskTypeEnum, UserDto userDto, String batchId, OperateMethodEnum operateMethod) {
        //每一个批次的uuid 用于确认是否为同一个批次
        //判断是否存在前置任务
        List<String> taskId = new ArrayList<>();
        if (QueueTaskTypeEnum.PREFIX_TASK.equals(queueTaskTypeEnum)) {
            if (!dataFactory.getPrefixTaskId().isEmpty()) {
                taskId = dataFactory.getPrefixTaskId();
            } else {
                //前置任务没有生成任务本身
                queueTaskTypeEnum = QueueTaskTypeEnum.ITSELF;
            }
        }
        if (QueueTaskTypeEnum.ITSELF.equals(queueTaskTypeEnum)) {
            //如果不存在前置任务就直接执行本任务
            taskId = Arrays.asList(dataFactory.getId());
        }
        if (QueueTaskTypeEnum.REAR_TASK.equals(queueTaskTypeEnum) && !dataFactory.getRearTaskId().isEmpty()) {
            taskId = dataFactory.getRearTaskId();
        }
        this.send(queueTaskTypeEnum, taskId, dataFactory.getId(), batchId, userDto, operateMethod, dataFactory.getViewJson());

    }

    /**
     * @param queueTaskTypeEnum 任务类型
     * @param taskItselfId      任务本身id
     * @param ids               需要入队列的数据id
     * @return 生成的队列id
     */
    private void send(QueueTaskTypeEnum queueTaskTypeEnum, List<String> ids, String taskItselfId, String batchId, UserDto userDto, OperateMethodEnum operateMethod, String executionGraph) {
        ids.forEach(e -> {
            DataFactoryTaskMqDto dataFactoryTaskMqDto = new DataFactoryTaskMqDto()
                    .setDataFactoryId(e)
                    .setUserDto(userDto)
                    .setOperateMethod(operateMethod)
                    .setTaskItselfId(taskItselfId)
                    .setExecutionGraph(executionGraph)
                    .setBatchId(batchId)
                    .setQueueTaskType(queueTaskTypeEnum)
                    .setTenantId(TenantContextHolder.getTenantId());
            if (!userDto.getTenantId().equals(TenantContextHolder.getTenantId())) {
                throw new BusinessException("租户id被变更,变更后的租户id为:{}", userDto.getTenantId());
            }
            try {
                DataFactoryTaskConsumer dataFactoryTaskConsumer = SpringContextUtil.getBean(DataFactoryTaskConsumer.class);
                dataFactoryTaskConsumer.send(dataFactoryTaskMqDto);
            } catch (Exception exception) {
                log.info("-------进入队列失败入参为:{}", JSONObject.toJSONString(dataFactoryTaskMqDto), exception);
                if (exception instanceof BusinessException) {
                    throw exception;
                } else {
                    throw new BusinessException("任务创建失败,请稍候再试!");
                }
            }
        });

    }


    @Override
    public void unLockDataFactory(String dataFactoryId) {
        redisUtils.unLock(RedisKey.getDataFactoryLockKey(dataFactoryId));
    }

    @Override
    public void unLockSyncOdsLock(String dataFactoryId, String nodeId) {
        redisUtils.unLock(RedisKey.getSyncDataLock(dataFactoryId, nodeId));
    }

    @Override
    public Boolean getDataFactoryIsLock(String dataFactoryId) {
        return redisUtils.tryLock(RedisKey.getDataFactoryLockKey(dataFactoryId), 1800);
    }

    @Override
    public JvsDataFactory upGetOrDataRecover(String id) {
        jvsDataFactoryMapper.retrieve(id);
        return this.getById(id);
    }

    @Override
    public Boolean getSyncOdsDataIsLock(String dataFactoryId, String nodeId) {
        return redisUtils.tryLock(RedisKey.getSyncDataLock(dataFactoryId, nodeId), 1800);
    }

}
