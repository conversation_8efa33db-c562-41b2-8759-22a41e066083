package cn.bctools.report.entity;

import cn.bctools.report.dto.TaskCronDto;
import cn.bctools.report.enums.ReportLoadMethodEnum;
import cn.bctools.report.enums.ReportTypeEnum;
import cn.bctools.report.enums.SourceEnum;
import cn.bctools.report.handler.TaskHandler;
import cn.bctools.report.model.BaseAuthPo;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 数据报表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Data
@ApiModel("数据报表")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "jvs_report", autoResultMap = true)
public class JvsReport extends BaseAuthPo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("设计")
    @TableField("name")
    private String name;
    @ApiModelProperty("排序")
    private Integer sort;
    @ApiModelProperty("渲染json")
    private String viewJson;
    @NotBlank(message = "类型不能为空")
    @ApiModelProperty("分类")
    private String type;
    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("来源")
    private SourceEnum source;

    @ApiModelProperty("数据源")
    @TableField(typeHandler = FastjsonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private List<JSONObject> dataSource;

    @ApiModelProperty("应用ID")
    private String jvsAppId;

    @ApiModelProperty("是否有定时任务")
    private Boolean cronStatus;

    @ApiModelProperty("是否有定时任务")
    @TableField(value = "cron_setting",typeHandler = TaskHandler.class,updateStrategy = FieldStrategy.IGNORED)
    private TaskCronDto cronSetting;

    @ApiModelProperty("租户id")
    private String tenantId;

    @ApiModelProperty("报表类型 静态报表 动态报表")
    private ReportTypeEnum reportType;

    @ApiModelProperty("主表id 动态报表没有主表")
    private String mainReportId;
    
    @ApiModelProperty("加载方式")
    private ReportLoadMethodEnum loadMethod;

    @TableField(exist = false)
    @ApiModelProperty("静态报表列表")
    List<JvsReport> staticList;

    public void clear(){
        this.setCreateById(null);
        this.setCreateTime(null);
        this.setUpdateBy(null);
        this.setUpdateTime(null);
        this.setTenantId(null);
        this.setCreateBy(null);
        this.setRoleType(false);
        this.setRole(new ArrayList<>());
        this.setType(null);
    }
}
