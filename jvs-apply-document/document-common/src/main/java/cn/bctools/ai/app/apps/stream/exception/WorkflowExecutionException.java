package cn.bctools.ai.app.apps.stream.exception;

import lombok.Getter;

/**
 * 工作流执行异常
 * 统一的工作流执行异常处理
 *
 * <AUTHOR>
 */
@Getter
public class WorkflowExecutionException extends RuntimeException {

    /**
     * 执行ID
     */
    private final String executionId;

    /**
     * 应用ID
     */
    private final String appId;

    /**
     * 错误代码
     */
    private final WorkflowErrorCode errorCode;

    /**
     * 错误详情
     */
    private final String errorDetail;

    /**
     * 构造函数
     *
     * @param executionId 执行ID
     * @param appId       应用ID
     * @param errorCode   错误代码
     * @param message     错误消息
     */
    public WorkflowExecutionException(String executionId, String appId, 
                                    WorkflowErrorCode errorCode, String message) {
        super(message);
        this.executionId = executionId;
        this.appId = appId;
        this.errorCode = errorCode;
        this.errorDetail = message;
    }

    /**
     * 构造函数（带原因）
     *
     * @param executionId 执行ID
     * @param appId       应用ID
     * @param errorCode   错误代码
     * @param message     错误消息
     * @param cause       原因
     */
    public WorkflowExecutionException(String executionId, String appId, 
                                    WorkflowErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.executionId = executionId;
        this.appId = appId;
        this.errorCode = errorCode;
        this.errorDetail = message;
    }

    /**
     * 获取完整的错误信息
     */
    public String getFullErrorMessage() {
        return String.format("[%s] 执行ID: %s, 应用ID: %s, 错误: %s", 
                           errorCode.getCode(), executionId, appId, getMessage());
    }

    /**
     * 创建业务异常
     */
    public static WorkflowExecutionException businessError(String executionId, String appId, 
                                                          String message, Throwable cause) {
        return new WorkflowExecutionException(executionId, appId, 
                                            WorkflowErrorCode.BUSINESS_ERROR, message, cause);
    }

    /**
     * 创建系统异常
     */
    public static WorkflowExecutionException systemError(String executionId, String appId, 
                                                        String message, Throwable cause) {
        return new WorkflowExecutionException(executionId, appId, 
                                            WorkflowErrorCode.SYSTEM_ERROR, message, cause);
    }

    /**
     * 创建验证异常
     */
    public static WorkflowExecutionException validationError(String executionId, String appId, 
                                                           String message) {
        return new WorkflowExecutionException(executionId, appId, 
                                            WorkflowErrorCode.VALIDATION_ERROR, message);
    }

    /**
     * 创建资源不足异常
     */
    public static WorkflowExecutionException resourceError(String executionId, String appId, 
                                                          String message) {
        return new WorkflowExecutionException(executionId, appId, 
                                            WorkflowErrorCode.RESOURCE_ERROR, message);
    }

    /**
     * 创建超时异常
     */
    public static WorkflowExecutionException timeoutError(String executionId, String appId, 
                                                         String message) {
        return new WorkflowExecutionException(executionId, appId, 
                                            WorkflowErrorCode.TIMEOUT_ERROR, message);
    }
}
