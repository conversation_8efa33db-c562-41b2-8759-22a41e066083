package cn.bctools.report.service.impl;

import cn.bctools.report.entity.JvsReportTaskLog;
import cn.bctools.report.enums.ExecuteStageEnum;
import cn.bctools.report.mapper.JvsReportTaskLogMapper;
import cn.bctools.report.service.JvsReportTaskLogService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 报表定时任务执行日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Service
public class JvsReportTaskLogServiceImpl extends ServiceImpl<JvsReportTaskLogMapper, JvsReportTaskLog> implements JvsReportTaskLogService {

    @Override
    public void success(String logId) {
        this.update(Wrappers.lambdaUpdate(JvsReportTaskLog.class)
                .set(JvsReportTaskLog::getExecuteStage, ExecuteStageEnum.success)
                .set(JvsReportTaskLog::getFinishTime, LocalDateTime.now())
                .eq(JvsReportTaskLog::getId,logId));
    }

    @Override
    public void failed(String logId, String error) {
        this.update(Wrappers.lambdaUpdate(JvsReportTaskLog.class)
                .set(JvsReportTaskLog::getExecuteStage, ExecuteStageEnum.error)
                .set(JvsReportTaskLog::getFinishTime, LocalDateTime.now())
                .set(JvsReportTaskLog::getExecuteMsg,error)
                .eq(JvsReportTaskLog::getId,logId));
    }
}
