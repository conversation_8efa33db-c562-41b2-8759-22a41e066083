package cn.bctools.data.factory.notice.send;

import cn.bctools.auth.api.api.UserExtensionServiceApi;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.enums.OtherLoginTypeEnum;
import cn.bctools.data.factory.notice.dto.DataNotifyDto;
import cn.bctools.data.factory.notice.enums.DingNoticeEnum;
import cn.bctools.data.factory.notice.enums.NoticeTypeEnum;
import cn.bctools.message.push.api.DingTalkCorpApi;
import cn.bctools.message.push.dto.messagepush.ReceiversDto;
import cn.bctools.message.push.dto.messagepush.dingtalk.corp.LinkMessageDTO;
import cn.bctools.message.push.dto.messagepush.dingtalk.corp.TextMessageDTO;
import com.alibaba.fastjson2.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: ZhuXiaoKang
 * @Description: 发送钉钉消息通知
 */
@Slf4j
@Component
@AllArgsConstructor
public class DingSendNotifyImpl implements SendNotifyHandler {
    private final DingTalkCorpApi api;
    private final UserExtensionServiceApi userExtensionServiceApi;

    @Override
    public String getType() {
        return NoticeTypeEnum.DING.getValue();
    }

    @Override
    public void send(DataNotifyDto dto) {
        //根据用户ID获取扩展ID
        List<String> userIds = dto.getUsers().stream().map(UserDto::getId).collect(Collectors.toList());
        List<ReceiversDto> definedReceivers = Optional.ofNullable(userExtensionServiceApi.query(userIds, OtherLoginTypeEnum.Ding.name()).getData()).orElse(new ArrayList<>())
                .stream()
                .map(s -> new ReceiversDto().setUserId(s.getId()).setReceiverConfig(String.valueOf(s.getExtension().get("userid"))).setUserName(s.getNickname()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(definedReceivers)) {
            log.warn("发送钉钉消息失败: 未找到用户");
            return;
        }
        Map<DingNoticeEnum, Object> dingNotice = dto.getTemplate().getDing();
        dingNotice.entrySet().parallelStream().forEach(w -> {
            switch (w.getKey()) {
                case TEXT:
                    sendText(dto.getClientId(), definedReceivers, dto.getContent(),dto.getTenantId());
                    break;
                case LinkMessage:
                    sendLinkMessage(dto.getClientId(), definedReceivers, dto.getTitle(), dto.getImgUrl(), dto.getContent(), dto.getMessageUrl(),dto.getTenantId());
                    break;
                default:
            }

        });
    }


    /**
     * 发送文本消息
     *
     * @param clientId
     * @param definedReceivers
     * @param content
     */
    private void sendText(String clientId, List<ReceiversDto> definedReceivers, String content,String tenantId) {
        TextMessageDTO dingMessage = new TextMessageDTO().setContent(content);
        dingMessage.setTenantId(tenantId);
        dingMessage.setDefinedReceivers(definedReceivers);
        dingMessage.setClientCode(clientId);
        log.debug("发送钉钉文本消息：{}", JSON.toJSONString(dingMessage));
        api.sendTextMessage(dingMessage);
        log.info("发送钉钉文本消息完成");
    }

    private void sendLinkMessage(String clientId, List<ReceiversDto> definedReceivers, String title, String url, String content, String messageUrl,String tenantId) {
        LinkMessageDTO dto = new LinkMessageDTO();
        dto.setTenantId(tenantId);
        dto.setPicUrl(url);
        dto.setTitle(title);
        dto.setClientCode(clientId);
        dto.setText(content);
        dto.setMessageUrl(messageUrl);
        dto.setDefinedReceivers(definedReceivers);
        log.debug("发送钉钉消息：{}", JSON.toJSONString(dto));
        api.sendLinkMessage(dto);
        log.info("发送钉钉消息完成");
    }

}
