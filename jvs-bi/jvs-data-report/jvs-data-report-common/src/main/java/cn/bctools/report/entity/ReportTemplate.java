package cn.bctools.report.entity;

import cn.bctools.database.entity.po.BasalPo;
import cn.bctools.database.entity.po.BasePo;
import cn.bctools.report.enums.SourceEnum;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 模板数据
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName(value = "jvs_report_template", autoResultMap = true)
@ApiModel
public class ReportTemplate extends BasalPo implements Serializable {

    private static final long serialVersionUID = -5623407547220439967L;
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("id")
    private String id;

    /**
     * 设计
     */
    @ApiModelProperty("设计")
    @TableField("name")
    private String name;

    /**
     * 渲染json
     */
    @ApiModelProperty("渲染json")
    private String viewJson;

    @NotBlank(message = "类型不能为空")
    @ApiModelProperty("分类")
    private String type;

    /**
     * 表单描述
     */
    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("来源")
    private SourceEnum source;

    @ApiModelProperty("数据源")
    @TableField(typeHandler = FastjsonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private List<JSONObject> dataSource;
    @ApiModelProperty("封面桶名称")
    private String coverFilePath;
    @ApiModelProperty("封面文件路径")
    private String coverBucketName;

}
