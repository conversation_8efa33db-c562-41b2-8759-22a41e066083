package cn.bctools.ai.app.apps.stream.limiter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 工作流执行限流器
 * 防止系统资源耗尽
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WorkflowRateLimiter {

    /**
     * 最大并发执行数量
     */
    @Value("${workflow.execution.max-concurrent:100}")
    private int maxConcurrentExecutions;

    /**
     * 获取许可的超时时间（秒）
     */
    @Value("${workflow.execution.acquire-timeout:5}")
    private int acquireTimeoutSeconds;

    /**
     * 信号量控制并发数
     */
    private Semaphore semaphore;

    /**
     * 当前活跃执行数
     */
    private final AtomicInteger activeExecutions = new AtomicInteger(0);

    /**
     * 总执行数
     */
    private final AtomicInteger totalExecutions = new AtomicInteger(0);

    /**
     * 被拒绝的执行数
     */
    private final AtomicInteger rejectedExecutions = new AtomicInteger(0);

    @PostConstruct
    public void init() {
        this.semaphore = new Semaphore(maxConcurrentExecutions, true);
        log.info("🚦 工作流限流器已初始化 - 最大并发数: {}, 获取超时: {}秒", 
                maxConcurrentExecutions, acquireTimeoutSeconds);
    }

    /**
     * 尝试获取执行许可
     *
     * @return 是否成功获取许可
     */
    public boolean tryAcquire() {
        try {
            boolean acquired = semaphore.tryAcquire(acquireTimeoutSeconds, TimeUnit.SECONDS);
            if (acquired) {
                activeExecutions.incrementAndGet();
                totalExecutions.incrementAndGet();
                log.debug("✅ 获取执行许可成功 - 当前活跃: {}/{}", 
                         activeExecutions.get(), maxConcurrentExecutions);
            } else {
                rejectedExecutions.incrementAndGet();
                log.warn("🚫 获取执行许可失败 - 当前活跃: {}/{}, 总拒绝: {}", 
                        activeExecutions.get(), maxConcurrentExecutions, rejectedExecutions.get());
            }
            return acquired;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            rejectedExecutions.incrementAndGet();
            log.error("❌ 获取执行许可被中断", e);
            return false;
        }
    }

    /**
     * 立即尝试获取许可（不等待）
     *
     * @return 是否成功获取许可
     */
    public boolean tryAcquireImmediately() {
        boolean acquired = semaphore.tryAcquire();
        if (acquired) {
            activeExecutions.incrementAndGet();
            totalExecutions.incrementAndGet();
            log.debug("✅ 立即获取执行许可成功 - 当前活跃: {}/{}", 
                     activeExecutions.get(), maxConcurrentExecutions);
        } else {
            rejectedExecutions.incrementAndGet();
            log.warn("🚫 立即获取执行许可失败 - 当前活跃: {}/{}", 
                    activeExecutions.get(), maxConcurrentExecutions);
        }
        return acquired;
    }

    /**
     * 释放执行许可
     */
    public void release() {
        semaphore.release();
        int current = activeExecutions.decrementAndGet();
        log.debug("🔓 释放执行许可 - 当前活跃: {}/{}", current, maxConcurrentExecutions);
    }

    /**
     * 强制释放许可（用于异常情况）
     */
    public void forceRelease() {
        try {
            semaphore.release();
            activeExecutions.decrementAndGet();
            log.warn("⚠️ 强制释放执行许可");
        } catch (Exception e) {
            log.error("❌ 强制释放许可失败", e);
        }
    }

    /**
     * 获取当前活跃执行数
     */
    public int getActiveExecutions() {
        return activeExecutions.get();
    }

    /**
     * 获取总执行数
     */
    public int getTotalExecutions() {
        return totalExecutions.get();
    }

    /**
     * 获取被拒绝的执行数
     */
    public int getRejectedExecutions() {
        return rejectedExecutions.get();
    }

    /**
     * 获取可用许可数
     */
    public int getAvailablePermits() {
        return semaphore.availablePermits();
    }

    /**
     * 获取最大并发数
     */
    public int getMaxConcurrentExecutions() {
        return maxConcurrentExecutions;
    }

    /**
     * 检查是否有可用许可
     */
    public boolean hasAvailablePermits() {
        return semaphore.availablePermits() > 0;
    }

    /**
     * 获取限流统计信息
     */
    public RateLimiterStats getStats() {
        return new RateLimiterStats(
                maxConcurrentExecutions,
                activeExecutions.get(),
                totalExecutions.get(),
                rejectedExecutions.get(),
                semaphore.availablePermits()
        );
    }

    /**
     * 限流统计信息
     */
    public static class RateLimiterStats {
        public final int maxConcurrent;
        public final int activeConcurrent;
        public final int totalExecutions;
        public final int rejectedExecutions;
        public final int availablePermits;

        public RateLimiterStats(int maxConcurrent, int activeConcurrent, 
                               int totalExecutions, int rejectedExecutions, int availablePermits) {
            this.maxConcurrent = maxConcurrent;
            this.activeConcurrent = activeConcurrent;
            this.totalExecutions = totalExecutions;
            this.rejectedExecutions = rejectedExecutions;
            this.availablePermits = availablePermits;
        }
    }
}
