package cn.bctools.report.mongo.service.impl;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.data.factory.config.DorisJdbcTemplate;
import cn.bctools.report.mongo.entity.MongoCellDoc;
import cn.bctools.report.mongo.service.StatisticReportService;
import cn.bctools.report.po.CellData;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DorisReportServiceImpl implements StatisticReportService {

    private final DorisJdbcTemplate dorisJdbcTemplate;

    private final static String TABLE_NAME = "report_{}";

    private final static String VALUES_SQL = "({},{},'{}')";

    private final static String CREATE_TABLE = "CREATE TABLE `{}` (\n" +
            "      `r` BIGINT NOT NULL,\n" +
            "      `c` BIGINT NOT NULL,\n" +
            "      `v` STRING\n" +
            ") ENGINE=OLAP\n" +
            "DISTRIBUTED BY HASH(`r`) BUCKETS 10\n" +
            "PROPERTIES (\n" +
            "\"replication_allocation\" = \"tag.location.default: 3\"\n" +
            ");";

    private final static String LIST_SQL = "SELECT * FROM `{}`";

    private final static String INSERT_SQL = "INSERT INTO `{}` (`r`,`c`,`v`) VALUES {}";

    @Override
    public void saveBatch(List<CellData> cellDataList, String collectionName) {
        List<MongoCellDoc> copys = BeanCopyUtil.copys(cellDataList, MongoCellDoc.class);
        String tableName = getTableName(collectionName);
        boolean b = dorisJdbcTemplate.ifNotExistsTable(tableName);
        if(!b){
            //建表
            String createTable = StrUtil.format(CREATE_TABLE, tableName);
            dorisJdbcTemplate.execute(createTable);
        }

        String valuesSql = copys.stream().map(e -> StrUtil.format(VALUES_SQL, e.getR(), e.getC(), JSONUtil.toJsonStr(e.getV()))).collect(Collectors.joining(","));

        String insertSql = StrUtil.format(INSERT_SQL, tableName, valuesSql);
        log.info("---doris insert: {}--",insertSql);
        dorisJdbcTemplate.execute(insertSql);
    }

    @Override
    public List<CellData> findAll(String collectionName) {
        String tableName = getTableName(collectionName);
        //如果不存在
        if(!dorisJdbcTemplate.ifNotExistsTable(tableName)){
            throw new BusinessException("静态数据不存在");
        }
        String query = StrUtil.format(LIST_SQL, tableName);
        List<Map<String, Object>> list = dorisJdbcTemplate.queryForList(query);
        if(CollectionUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        return BeanCopyUtil.copys(list,CellData.class);
    }

    @Override
    public void drop(String collectionName) {
        String tableName = this.getTableName(collectionName);
        dorisJdbcTemplate.drop(tableName);
    }

    @Override
    public void asyncDrop(List<String> collectionNameList) {
        for (String collectionName : collectionNameList) {
            drop(collectionName);
        }
    }

    private String getTableName(String collectionName){
        return StrUtil.format(TABLE_NAME,collectionName);
    }
}
