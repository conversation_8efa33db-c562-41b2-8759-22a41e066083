package cn.bctools.report.mongo.service;

import cn.bctools.report.po.CellData;

import java.util.List;

public interface StatisticReportService {

    /**
     * 批量保存
     * @param cellDataList 报表单元格数据
     * @param collectionName 表名称
     */
    void saveBatch(List<CellData> cellDataList,String collectionName);


    /**
     * 根据表名查询所有的数据
     * @param collectionName 表名称
     * @return 报表单元格数据
     */
    List<CellData> findAll(String collectionName);

    /**
     * 根据表名称删除
     * @param collectionName
     */
    void drop(String collectionName);

    /**
     * 根据表名称删除
     * @param collectionNameList
     */
    void asyncDrop(List<String> collectionNameList);
}
