package cn.bctools.report.data.search;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.data.factory.enums.DataFieldTypeClassifyEnum;
import cn.bctools.report.data.util.SortUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class Between implements DataQuery {

    private static final String FORMAT = "`{}` between ? and ?";

    @Override
    public boolean exec(DataFieldTypeClassifyEnum dataFieldTypeEnum, String format, Object value, String value1) {
        if(StrUtil.isEmpty(value1) || !JSONUtil.isTypeJSONArray(value1)){
            return true;
        }
        List<String> list = JSONObject.parseArray(value1, String.class);
        //区间必须是两个值
        if (list.size() < BigDecimal.ROUND_CEILING) {
            return true;
        }
        if (dataFieldTypeEnum.equals(DataFieldTypeClassifyEnum.数字)) {
            double aDouble = NumberUtil.parseDouble(StrUtil.toString(value));
            return NumberUtil.parseDouble(list.get(0)) <= aDouble && aDouble <= NumberUtil.parseDouble(list.get(1));
        }
        if (dataFieldTypeEnum.equals(DataFieldTypeClassifyEnum.时间)) {
            DateTime dateTime = SortUtil.convert2Date(StrUtil.toString(value), format);
            DateTime dateTime1 = SortUtil.convert2Date(list.get(0), format);
            DateTime dateTime2 = SortUtil.convert2Date(list.get(1), format);
            return DateUtil.isIn(dateTime, dateTime1, dateTime2);
        }
        return false;
    }

    @Override
    public SqlQueryDto generateSql(DataFieldTypeClassifyEnum dataFieldTypeEnum, String key, String value) {
        if(StrUtil.isNotBlank(value) && JSONUtil.isTypeJSONArray(value)){
            List<String> list = JSONObject.parseArray(value, String.class);
            //区间必须是两个值
            if (list.size() == BigDecimal.ROUND_CEILING) {
                String sql = StrUtil.format(FORMAT, key);
                List<Object> args = new ArrayList<>(list);
                if (dataFieldTypeEnum.equals(DataFieldTypeClassifyEnum.数字)) {
                    args = list.stream().map(NumberUtil::parseNumber).collect(Collectors.toList());
                }
                return new SqlQueryDto().setSql(sql).setArgs(args);
            }
        }
        throw new BusinessException("between sql片段生成失败");
    }

}
