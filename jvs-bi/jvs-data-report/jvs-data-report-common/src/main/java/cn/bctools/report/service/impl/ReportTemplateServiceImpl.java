package cn.bctools.report.service.impl;

import cn.bctools.report.entity.ReportTemplate;
import cn.bctools.report.mapper.ReportTemplateMapper;
import cn.bctools.report.service.ReportTemplateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 模板
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class ReportTemplateServiceImpl extends ServiceImpl<ReportTemplateMapper, ReportTemplate> implements ReportTemplateService {


}
