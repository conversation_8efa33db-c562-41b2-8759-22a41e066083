package cn.bctools.data.factory.service.impl;

import cn.bctools.data.factory.entity.FieldSettingFunction;
import cn.bctools.data.factory.mapper.FieldSettingFunctionMapper;
import cn.bctools.data.factory.service.FieldSettingFunctionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class FieldSettingFucntionServiceImpl extends ServiceImpl<FieldSettingFunctionMapper, FieldSettingFunction> implements FieldSettingFunctionService {


}
