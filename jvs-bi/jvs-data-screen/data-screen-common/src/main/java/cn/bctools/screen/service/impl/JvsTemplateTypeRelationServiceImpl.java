package cn.bctools.screen.service.impl;

import cn.bctools.screen.entity.JvsTemplateTypeRelation;
import cn.bctools.screen.mapper.JvsTemplateTypeRelationMapper;
import cn.bctools.screen.service.JvsTemplateTypeRelationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 模板分类与模板数据关系
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class JvsTemplateTypeRelationServiceImpl extends ServiceImpl<JvsTemplateTypeRelationMapper, JvsTemplateTypeRelation> implements JvsTemplateTypeRelationService {


}
