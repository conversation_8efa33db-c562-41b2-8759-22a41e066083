package cn.bctools.report.data.component.subtotalConfiguration.service;

import cn.bctools.report.data.component.subtotalConfiguration.SubtotalBaseService;
import cn.bctools.report.data.util.FillCellDataUtils;
import cn.bctools.report.po.CellData;
import cn.hutool.core.util.NumberUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 求和
 */
@Service
public class SummationServiceImpl implements SubtotalBaseService {

    @Override
    public BigDecimal exec(CellData setting, BigDecimal[] bigDecimals) {
        return NumberUtil.add(bigDecimals);
    }
}
