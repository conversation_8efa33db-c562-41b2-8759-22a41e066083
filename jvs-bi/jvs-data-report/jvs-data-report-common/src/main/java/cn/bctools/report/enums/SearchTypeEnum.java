package cn.bctools.report.enums;

import cn.bctools.report.data.search.Between;
import cn.bctools.report.data.search.DataQuery;
import cn.bctools.report.data.search.Eq;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SearchTypeEnum {
        input( Eq.class),
        range( Between.class),
        select(Eq.class);
        public Class<? extends DataQuery> bClass;

}
