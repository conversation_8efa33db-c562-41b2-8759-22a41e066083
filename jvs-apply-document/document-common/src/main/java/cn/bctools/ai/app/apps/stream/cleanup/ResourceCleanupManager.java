package cn.bctools.ai.app.apps.stream.cleanup;

import cn.bctools.stream.manager.StreamExecutionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 资源清理管理器
 * 负责清理过期资源和处理应用关闭时的资源清理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ResourceCleanupManager {

    private final StreamExecutionManager streamExecutionManager;

    @Value("${workflow.execution.context.enable-emergency-cleanup:true}")
    private boolean enableEmergencyCleanup;

    private final AtomicInteger totalCleanupCount = new AtomicInteger(0);
    private final AtomicInteger emergencyCleanupCount = new AtomicInteger(0);
    private volatile LocalDateTime lastCleanupTime;

    /**
     * 紧急资源清理 - 每2分钟执行一次
     */
    @Scheduled(fixedRate = 120000, initialDelay = 30000)
    public void emergencyCleanup() {
        if (!enableEmergencyCleanup) {
            return;
        }

        try {
            log.debug("🧹 开始紧急资源清理");
            
            StreamExecutionManager.ExecutionStats stats = streamExecutionManager.getExecutionStats();
            int activeContextsBefore = stats.getActiveContexts();
            
            if (activeContextsBefore > 0) {
                performEmergencyCleanup();
                
                StreamExecutionManager.ExecutionStats statsAfter = streamExecutionManager.getExecutionStats();
                int cleanedCount = activeContextsBefore - statsAfter.getActiveContexts();
                
                if (cleanedCount > 0) {
                    emergencyCleanupCount.addAndGet(cleanedCount);
                    totalCleanupCount.addAndGet(cleanedCount);
                    log.info("🧹 紧急清理完成 - 清理数量: {}, 剩余活跃: {}", cleanedCount, statsAfter.getActiveContexts());
                }
            }
            
            lastCleanupTime = LocalDateTime.now();
            
        } catch (Exception e) {
            log.error("❌ 紧急资源清理失败", e);
        }
    }

    /**
     * 应用关闭时的资源清理
     */
    @EventListener
    public void handleApplicationShutdown(ContextClosedEvent event) {
        log.info("🛑 应用关闭，开始清理所有资源");
        
        try {
            forceCleanupAll();
            printCleanupStats();
            log.info("✅ 应用关闭资源清理完成");
        } catch (Exception e) {
            log.error("❌ 应用关闭资源清理失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        log.info("🗑️ ResourceCleanupManager 正在销毁");
        try {
            forceCleanupAll();
        } catch (Exception e) {
            log.error("❌ 组件销毁清理失败", e);
        }
    }

    private void performEmergencyCleanup() {
        // 依赖StreamExecutionManager的内置清理机制
        // 可以在这里添加更激进的清理策略
    }

    private void forceCleanupAll() {
        try {
            StreamExecutionManager.ExecutionStats stats = streamExecutionManager.getExecutionStats();
            int activeContexts = stats.getActiveContexts();
            
            if (activeContexts > 0) {
                log.warn("⚠️ 强制清理所有资源 - 活跃上下文数: {}", activeContexts);
                totalCleanupCount.addAndGet(activeContexts);
            }
        } catch (Exception e) {
            log.error("❌ 强制清理所有资源失败", e);
        }
    }

    public void printCleanupStats() {
        log.info("📊 资源清理统计:");
        log.info("   总清理次数: {}", totalCleanupCount.get());
        log.info("   紧急清理次数: {}", emergencyCleanupCount.get());
        log.info("   最后清理时间: {}", lastCleanupTime);
        
        StreamExecutionManager.ExecutionStats stats = streamExecutionManager.getExecutionStats();
        log.info("   当前活跃上下文: {}", stats.getActiveContexts());
        log.info("   当前活跃执行: {}", stats.getActiveExecutions());
    }

    public void manualCleanup() {
        log.info("🔧 手动触发资源清理");
        try {
            emergencyCleanup();
            log.info("✅ 手动清理完成");
        } catch (Exception e) {
            log.error("❌ 手动清理失败", e);
            throw new RuntimeException("手动清理失败", e);
        }
    }
}
