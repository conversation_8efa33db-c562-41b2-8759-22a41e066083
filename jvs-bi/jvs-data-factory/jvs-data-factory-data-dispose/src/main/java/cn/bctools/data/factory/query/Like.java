package cn.bctools.data.factory.query;


import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 包含 字符串
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class Like implements Query {

    @Override
    public List<Object> exec(QueryExecDto queryExecDto, StringBuffer whereSql) {
        whereSql.append("`").append(queryExecDto.getFieldKey()).append("`")
                .append("like")
                .append("('%")
                .append(queryExecDto.getMethodValue())
                .append("%')");
        return new ArrayList<>();
    }

}
