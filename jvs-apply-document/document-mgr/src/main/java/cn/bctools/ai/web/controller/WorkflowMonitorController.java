package cn.bctools.ai.web.controller;

import cn.bctools.ai.app.apps.stream.cleanup.ResourceCleanupManager;
import cn.bctools.ai.app.apps.stream.limiter.WorkflowRateLimiter;
import cn.bctools.ai.app.apps.stream.metrics.WorkflowExecutionMetrics;
import cn.bctools.common.utils.R;
import cn.bctools.stream.manager.StreamExecutionManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 工作流监控控制器
 * 提供工作流执行的监控和管理接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/workflow/monitor")
@Api(tags = "工作流监控")
@RequiredArgsConstructor
public class WorkflowMonitorController {

    private final WorkflowExecutionMetrics metrics;
    private final WorkflowRateLimiter rateLimiter;
    private final StreamExecutionManager streamExecutionManager;
    private final ResourceCleanupManager cleanupManager;

    @GetMapping("/metrics")
    @ApiOperation("获取工作流执行指标")
    public R<WorkflowExecutionMetrics.ExecutionStats> getMetrics() {
        try {
            WorkflowExecutionMetrics.ExecutionStats stats = metrics.getDetailedStats();
            return R.ok(stats);
        } catch (Exception e) {
            log.error("❌ 获取执行指标失败", e);
            return R.failed("获取执行指标失败: " + e.getMessage());
        }
    }

    @GetMapping("/rate-limiter")
    @ApiOperation("获取限流器状态")
    public R<WorkflowRateLimiter.RateLimiterStats> getRateLimiterStats() {
        try {
            WorkflowRateLimiter.RateLimiterStats stats = rateLimiter.getStats();
            return R.ok(stats);
        } catch (Exception e) {
            log.error("❌ 获取限流器状态失败", e);
            return R.failed("获取限流器状态失败: " + e.getMessage());
        }
    }

    @GetMapping("/health")
    @ApiOperation("获取工作流系统健康状态")
    public R<Map<String, Object>> getHealthStatus() {
        try {
            Map<String, Object> health = new HashMap<>();
            
            // 执行指标
            WorkflowExecutionMetrics.ExecutionStats execStats = metrics.getDetailedStats();
            health.put("executionMetrics", Map.of(
                "totalExecutions", execStats.totalExecutions,
                "successRate", execStats.successRate,
                "failureRate", execStats.failureRate,
                "averageExecutionTime", execStats.averageExecutionTime
            ));
            
            // 限流器状态
            WorkflowRateLimiter.RateLimiterStats rateLimiterStats = rateLimiter.getStats();
            health.put("rateLimiter", Map.of(
                "activeConcurrent", rateLimiterStats.activeConcurrent,
                "maxConcurrent", rateLimiterStats.maxConcurrent,
                "availablePermits", rateLimiterStats.availablePermits
            ));
            
            // 系统状态评估
            String status = evaluateSystemHealth(execStats, rateLimiterStats);
            health.put("overallStatus", status);
            health.put("timestamp", System.currentTimeMillis());
            
            return R.ok(health);
            
        } catch (Exception e) {
            log.error("❌ 获取健康状态失败", e);
            return R.failed("获取健康状态失败: " + e.getMessage());
        }
    }

    @PostMapping("/cleanup/manual")
    @ApiOperation("手动触发资源清理")
    public R<String> manualCleanup() {
        try {
            cleanupManager.manualCleanup();
            return R.ok("手动清理完成");
        } catch (Exception e) {
            log.error("❌ 手动清理失败", e);
            return R.failed("手动清理失败: " + e.getMessage());
        }
    }

    @PostMapping("/metrics/reset")
    @ApiOperation("重置执行指标")
    public R<String> resetMetrics() {
        try {
            metrics.reset();
            return R.ok("执行指标已重置");
        } catch (Exception e) {
            log.error("❌ 重置指标失败", e);
            return R.failed("重置指标失败: " + e.getMessage());
        }
    }

    /**
     * 评估系统健康状态
     */
    private String evaluateSystemHealth(WorkflowExecutionMetrics.ExecutionStats execStats,
                                       WorkflowRateLimiter.RateLimiterStats rateLimiterStats) {
        
        // 检查失败率
        if (execStats.failureRate > 50) {
            return "CRITICAL";
        }
        
        // 检查系统负载
        double systemLoad = (double) rateLimiterStats.activeConcurrent / rateLimiterStats.maxConcurrent;
        if (systemLoad > 0.9) {
            return "WARNING";
        }
        
        // 检查拒绝率
        if (rateLimiterStats.rejectedExecutions > 0) {
            double rejectionRate = (double) rateLimiterStats.rejectedExecutions / rateLimiterStats.totalExecutions;
            if (rejectionRate > 0.1) {
                return "WARNING";
            }
        }
        
        return "HEALTHY";
    }
}
