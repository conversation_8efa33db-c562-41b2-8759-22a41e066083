package cn.bctools.data.factory.source.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum DataSourceTypeEnum {
    mysqlDataSource("mysqlDataSource", "mysql数据库", Boolean.TRUE),
    mongoDataSource("mongoDataSource", "mongodb数据库", Boolean.FALSE),
    dataFactoryDataSource("dataFactoryDataSource", "数据集", Boolean.FALSE),
    pgsqlDataSource("pgsqlDataSource", "pgsql数据源", Boolean.FALSE),
    dataModel("dataModel", "数据模型", Boolean.FALSE),
    mariadbDataSource("mariadbDataSource", "mariadb数据库", Boolean.FALSE),
    oracleDataSource("oracleDataSource", "oracle数据库", Boolean.TRUE),
    dMDataSource("dMDataSource", "达梦数据库", Boolean.FALSE),
    db2DataSource("db2DataSource", "db2数据库", Boolean.FALSE),
    excelDataSource("excelDataSource", "excel数据源", Boolean.FALSE),
    kingbaseDataSource("kingbaseDataSource", "人大金仓", Boolean.FALSE),
    sqlServerDataSource("sqlServerDataSource", "sqlServer数据库", Boolean.FALSE),
    tidbDataSource("tidbDataSource", "TiDB数据库", Boolean.FALSE),
    starRocksDataSource("starRocksDataSource","starRocks数据库", Boolean.FALSE),
    dorisDataSource("dorisDataSource","doris数据库", Boolean.FALSE),
    hiveDateSource("hiveDateSource","Apache Hive", Boolean.FALSE),
    clickHouseDataSource("clickHouseDataSource","Click House", Boolean.FALSE),
    prestoDataBase("prestoDataBase","prestoDB", Boolean.FALSE),
    apiDataSource("apiDataSource", "api数据源", Boolean.FALSE);

    @EnumValue
    public final String value;
    public final String desc;
    public final Boolean execSql;
}
