package cn.bctools.data.factory.aspect;

import cn.bctools.common.utils.TenantContextHolder;
import cn.bctools.data.factory.annotation.IgnoreTenant;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

@Aspect
@Slf4j
@Component
public class IgnoreTenantAop {

    @Before("@annotation(ignoreTenant)")
    public void before(JoinPoint joinPoint, IgnoreTenant ignoreTenant) throws Throwable {
        TenantContextHolder.clear();
    }
}
