package cn.bctools.report.cache;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;

import java.util.ArrayList;
import java.util.List;

public class ReportBorderInfoCache {

    private static final ThreadLocal<List<JSONObject>> local = new TransmittableThreadLocal<List<JSONObject>>();

    public static List<JSONObject> get(){
        List<JSONObject> jsonObjects = local.get();
        if(CollectionUtil.isEmpty(jsonObjects)){
            return new ArrayList<>();
        }
        return jsonObjects;
    }

    public static void set(List<JSONObject> list){
        if(ObjectUtil.isNull(local.get())){
            local.set(list);
        }else{
            List<JSONObject> jsonObjects = local.get();
            jsonObjects.addAll(list);
            local.set(jsonObjects);
        }
    }

    public static void remove(){
        local.remove();
    }
}
