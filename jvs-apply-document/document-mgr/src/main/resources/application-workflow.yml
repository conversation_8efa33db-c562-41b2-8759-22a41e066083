# 工作流执行配置
workflow:
  execution:
    # 最大并发执行数量
    max-concurrent: 100
    # 获取许可超时时间（秒）
    acquire-timeout: 5
    # 线程池配置
    thread-pool:
      # 核心线程数
      core-size: 10
      # 最大线程数
      max-size: 50
      # 线程空闲时间（秒）
      keep-alive: 60
      # 队列容量
      queue-capacity: 1000
    # 上下文清理配置
    context:
      # 上下文过期时间（分钟）
      expire-minutes: 30
      # 清理间隔（分钟）
      cleanup-interval: 5
    # 超时配置
    timeout:
      # 默认执行超时时间（秒）
      default-execution: 300
      # SSE连接超时时间（秒）
      sse-connection: 30
      # WebSocket连接超时时间（秒）
      websocket-connection: 60

# Spring配置
spring:
  # 事务配置
  transaction:
    # 事务超时时间（秒）
    default-timeout: 300
    # 回滚异常
    rollback-for: java.lang.Exception

# 日志配置
logging:
  level:
    cn.bctools.ai.app.apps.stream: DEBUG
    cn.bctools.stream: DEBUG
    cn.bctools.design.rule: INFO
