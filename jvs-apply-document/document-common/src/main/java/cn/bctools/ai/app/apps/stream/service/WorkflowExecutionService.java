package cn.bctools.ai.app.apps.stream.service;

import cn.bctools.ai.app.entity.AppDetail;
import cn.bctools.ai.app.entity.AppDetailContext;
import cn.bctools.ai.app.enums.AppMode;
import cn.bctools.stream.constants.StreamConstants;
import cn.bctools.stream.dto.StreamExecutionContext;
import cn.bctools.ai.app.apps.stream.dto.WorkflowExecutionRequest;
import cn.bctools.ai.app.apps.stream.dto.WorkflowExecutionResponse;
import cn.bctools.ai.app.apps.stream.factory.WorkflowExecutionProcessorFactory;
import cn.bctools.ai.app.apps.stream.processor.WorkflowExecutionProcessor;
import cn.bctools.ai.app.apps.stream.exception.WorkflowExecutionException;
import cn.bctools.ai.app.apps.stream.exception.WorkflowErrorCode;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.R;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.stream.manager.StreamExecutionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Map;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * 工作流执行服务
 * 统一处理工作流执行的业务逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkflowExecutionService {

    private final WorkflowExecutionProcessorFactory processorFactory;
    private final StreamExecutionManager streamExecutionManager;
    private final TransactionTemplate transactionTemplate;

    /**
     * 工作流执行限流器
     */
    private final Semaphore executionLimiter = new Semaphore(100, true);

    /**
     * 获取许可超时时间（秒）
     */
    private static final int ACQUIRE_TIMEOUT_SECONDS = 5;

    /**
     * 执行工作流（改进版本）
     *
     * @param request      执行请求
     * @param httpRequest  HTTP请求
     * @param httpResponse HTTP响应
     * @return 执行结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Object executeWorkflow(@Valid WorkflowExecutionRequest request,
                                  HttpServletRequest httpRequest,
                                  HttpServletResponse httpResponse) {

        String executionId = null;
        boolean permitAcquired = false;

        try {
            // 1. 限流控制
            if (!executionLimiter.tryAcquire(ACQUIRE_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                throw WorkflowExecutionException.resourceError(null, null,
                    "系统繁忙，请稍后重试。当前并发执行数已达上限");
            }
            permitAcquired = true;

            // 2. 获取应用详情
            AppDetail appDetail = getAppDetailSafely();
            executionId = "exec_" + System.currentTimeMillis();

            // 3. 验证应用类型
            validateAppTypeSafely(appDetail, executionId);

            // 4. 获取当前用户
            UserDto currentUser = getCurrentUserSafely(executionId, appDetail.getId());

            // 5. 记录执行开始
            log.info("🚀 开始执行工作流 - 应用ID: {}, 用户: {}, 执行ID: {}, 流式类型: {}",
                    appDetail.getId(), currentUser.getRealName(), executionId, request.getStreamType());

            // 6. 选择合适的处理器
            WorkflowExecutionProcessor processor = getProcessorSafely(request, httpRequest, executionId, appDetail.getId());

            // 7. 执行工作流
            Object result = executeWorkflowSafely(processor, appDetail, currentUser, request,
                                                httpRequest, httpResponse, executionId);

            log.info("✅ 工作流执行完成 - 应用ID: {}, 执行ID: {}, 处理器: {}",
                    appDetail.getId(), executionId, processor.getClass().getSimpleName());

            return result;

        } catch (WorkflowExecutionException e) {
            log.error("❌ 工作流执行失败 - 执行ID: {}, 错误: {}", executionId, e.getFullErrorMessage());
            throw e;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw WorkflowExecutionException.timeoutError(executionId, null, "获取执行许可被中断");
        } catch (Exception e) {
            log.error("❌ 工作流执行异常 - 执行ID: {}, 错误: {}", executionId, e.getMessage(), e);
            throw WorkflowExecutionException.systemError(executionId, null, "系统内部错误", e);
        } finally {
            // 8. 释放限流许可
            if (permitAcquired) {
                executionLimiter.release();
                log.debug("🔓 释放执行许可 - 执行ID: {}", executionId);
            }
        }
    }

    /**
     * 获取执行状态
     *
     * @param executionId 执行ID
     * @return 执行状态
     */
    public R<WorkflowExecutionResponse> getExecutionStatus(String executionId) {
        StreamExecutionContext context = streamExecutionManager.getContext(executionId);
        if (context == null) {
            return R.failed("执行上下文不存在");
        }

        WorkflowExecutionResponse response = buildExecutionResponse(context);
        return R.ok(response);
    }

    /**
     * 取消执行
     *
     * @param executionId 执行ID
     * @return 取消结果
     */
    public R<String> cancelExecution(String executionId) {
        try {
            streamExecutionManager.cancelExecution(executionId);
            log.info("🚫 工作流执行已取消 - 执行ID: {}", executionId);
            return R.ok("执行已取消");
        } catch (Exception e) {
            log.error("❌ 取消执行失败 - 执行ID: {}, 错误: {}", executionId, e.getMessage());
            return R.failed("取消执行失败: " + e.getMessage());
        }
    }


    /**
     * 获取执行统计
     *
     * @return 执行统计
     */
    public R<StreamExecutionManager.ExecutionStats> getExecutionStats() {
        StreamExecutionManager.ExecutionStats stats = streamExecutionManager.getExecutionStats();
        return R.ok(stats);
    }

    /**
     * 获取处理器信息
     *
     * @return 处理器信息
     */
    public R<Map<String, Object>> getProcessorInfo() {
        Map<String, Object> info = processorFactory.getProcessorInfo();
        return R.ok(info);
    }


    /**
     * 安全获取应用详情
     */
    private AppDetail getAppDetailSafely() {
        try {
            AppDetail appDetail = AppDetailContext.getObject();
            if (appDetail == null) {
                throw WorkflowExecutionException.validationError(null, null, "应用详情不存在");
            }
            return appDetail;
        } catch (Exception e) {
            if (e instanceof WorkflowExecutionException) {
                throw e;
            }
            throw WorkflowExecutionException.systemError(null, null, "获取应用详情失败", e);
        }
    }

    /**
     * 安全验证应用类型
     */
    private void validateAppTypeSafely(AppDetail appDetail, String executionId) {
        try {
            if (!AppMode.WORKFLOW.getValue().equals(appDetail.getMode())) {
                throw WorkflowExecutionException.validationError(executionId, appDetail.getId(),
                    "无效的应用类型，期望: " + AppMode.WORKFLOW.getValue() + ", 实际: " + appDetail.getMode());
            }
        } catch (Exception e) {
            if (e instanceof WorkflowExecutionException) {
                throw e;
            }
            throw WorkflowExecutionException.systemError(executionId, appDetail.getId(), "验证应用类型失败", e);
        }
    }

    /**
     * 安全获取当前用户
     */
    private UserDto getCurrentUserSafely(String executionId, String appId) {
        try {
            UserDto currentUser = UserCurrentUtils.getCurrentUser();
            if (currentUser == null) {
                throw WorkflowExecutionException.validationError(executionId, appId, "用户未认证");
            }
            return currentUser;
        } catch (Exception e) {
            if (e instanceof WorkflowExecutionException) {
                throw e;
            }
            throw WorkflowExecutionException.systemError(executionId, appId, "获取用户信息失败", e);
        }
    }

    /**
     * 安全获取处理器
     */
    private WorkflowExecutionProcessor getProcessorSafely(WorkflowExecutionRequest request,
                                                         HttpServletRequest httpRequest,
                                                         String executionId, String appId) {
        try {
            WorkflowExecutionProcessor processor = processorFactory.getBestProcessor(request, httpRequest);
            if (processor == null) {
                throw WorkflowExecutionException.validationError(executionId, appId, "未找到合适的处理器");
            }
            return processor;
        } catch (Exception e) {
            if (e instanceof WorkflowExecutionException) {
                throw e;
            }
            throw WorkflowExecutionException.systemError(executionId, appId, "获取处理器失败", e);
        }
    }

    /**
     * 安全执行工作流
     */
    private Object executeWorkflowSafely(WorkflowExecutionProcessor processor, AppDetail appDetail,
                                       UserDto currentUser, WorkflowExecutionRequest request,
                                       HttpServletRequest httpRequest, HttpServletResponse httpResponse,
                                       String executionId) {
        try {
            return processor.processExecution(appDetail, currentUser, request, httpRequest, httpResponse);
        } catch (BusinessException e) {
            throw WorkflowExecutionException.businessError(executionId, appDetail.getId(), e.getMessage(), e);
        } catch (Exception e) {
            throw WorkflowExecutionException.systemError(executionId, appDetail.getId(), "处理器执行失败", e);
        }
    }

    /**
     * 构建执行响应
     */
    private WorkflowExecutionResponse buildExecutionResponse(StreamExecutionContext context) {
        WorkflowExecutionResponse response = new WorkflowExecutionResponse()
                .setExecutionId(context.getExecutionId())
                .setAppId(context.getAppId())
                .setOutputType(context.getOutputType())
                .setStartTime(context.getStartTime())
                .setMetadata(context.getAttributes());

        // 设置状态
        if (context.isStreamingEnabled()) {
            response.setStatus("running");
        } else {
            response.setStatus("completed");
        }

        // 设置统计信息
        if (context.getStats() != null) {
            WorkflowExecutionResponse.ExecutionStats stats = new WorkflowExecutionResponse.ExecutionStats()
                    .setTotalNodes(context.getStats().getTotalNodes())
                    .setExecutedNodes(context.getStats().getExecutedNodes())
                    .setFailedNodes(context.getStats().getFailedNodes())
                    .setProgress(context.getStats().getProgress())
                    .setTotalDuration(context.getStats().getTotalDuration())
                    .setAverageNodeDuration(context.getStats().getAverageNodeDuration());

            response.setStats(stats);
        }

        // 设置连接URL
        String baseUrl = getBaseUrl();
        response.setWebsocketUrl(baseUrl + "/ws/workflow-execution/" + context.getExecutionId())
                .setSseUrl(baseUrl + "/api/workflows/stream/" + context.getExecutionId());

        return response;
    }

    /**
     * 获取基础URL
     */
    private String getBaseUrl() {
        // 返回空字符串，使用相对路径
        return "";
    }
}