package cn.bctools.report.cache;

import cn.bctools.report.po.CellData;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.ttl.TransmittableThreadLocal;

import java.util.List;

public class ReportExecuteCache {

    private static final ThreadLocal<List<CellData>> local = new TransmittableThreadLocal<List<CellData>>();

    public static List<CellData> get(){
        return local.get();
    }

    public static void set(List<CellData> list){
        if(CollectionUtil.isEmpty(local.get())){
            local.set(list);
        }else{
            local.get().addAll(list);
        }
    }

    public static void remove(){
        local.remove();
    }
}
