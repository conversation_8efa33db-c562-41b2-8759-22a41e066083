package cn.bctools.report.entity;

import cn.bctools.report.enums.ExecuteStageEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 报表定时任务执行日志
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Data
@Accessors(chain = true)
@TableName("jvs_report_task_log")
@ApiModel(value = "JvsReportTaskLog对象", description = "报表定时任务执行日志")
public class JvsReportTaskLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("日志id")
    @TableId(value = "id",type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("原始报表id")
    @TableField("report_id")
    private String reportId;

    @ApiModelProperty("原始报表名称")
    @TableField("report_name")
    private String reportName;

    @ApiModelProperty("完成时间")
    @TableField("finish_time")
    private LocalDateTime finishTime;

    @ApiModelProperty("执行状态")
    @TableField("execute_stage")
    private ExecuteStageEnum executeStage;

    @ApiModelProperty("执行信息（异常信息或执行完成信息）")
    @TableField("execute_msg")
    private String executeMsg;

    @ApiModelProperty("租户id")
    @TableField("tenant_id")
    private String tenantId;


    @ApiModelProperty("静态报表id")
    @TableField("static_report_id")
    private String staticReportId;

    @ApiModelProperty("静态报表名称")
    @TableField("static_report_name")
    private String staticReportName;

    @ApiModelProperty("开始时间")
    @TableField("start_time")
    private LocalDateTime startTime;

}
