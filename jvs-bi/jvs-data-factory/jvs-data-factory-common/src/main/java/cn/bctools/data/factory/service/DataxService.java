package cn.bctools.data.factory.service;


import cn.bctools.data.factory.dto.DataSourceField;
import cn.bctools.data.factory.html.node.params.InputParams;

import java.util.List;

/**
 * datax的所有操作抽象类
 *
 * <AUTHOR>
 */
public interface DataxService {

    /**
     * 同步
     * 上游必须把不需要同步的数据过滤掉
     *
     * @param structureId        表结构id
     * @param dataSourceFields   需要同步的结构
     * @param size               是否存在数量限制
     * @param dorisTableName     表名称
     * @param incrementalSetting 增量配置
     */
    void syncData(String structureId, List<DataSourceField> dataSourceFields, Long size, String dorisTableName, InputParams.IncrementalSetting incrementalSetting);
}
