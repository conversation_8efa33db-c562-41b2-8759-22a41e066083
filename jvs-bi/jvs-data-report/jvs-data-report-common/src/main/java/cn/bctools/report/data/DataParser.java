package cn.bctools.report.data;

import cn.bctools.report.po.CellData;
import cn.bctools.report.po.ReportDataPo;

/**
 * 公共类 执行顺序用 order来决定 order 值会预留 防止 出现中途添加一个链
 */
public interface DataParser {
    /**
     * 判断是否需要走此设计
     *
     * @param cell 用户设计数据
     * @return 是否符合当前执行逻辑 {@link Boolean}
     */
    default boolean check(CellData cell) {
        return cell.getNeedExecute() && !cell.getIsFunction();
    }

    /**
     * 生成数据
     *
     * @param cell         用户设计的数据
     * @param reportDataPo 表格数据
     * @return 是否可以终止链表 {@link Boolean}
     */
    void generatedData(CellData cell, ReportDataPo reportDataPo);
}
